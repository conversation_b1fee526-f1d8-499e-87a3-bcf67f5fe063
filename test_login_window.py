#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试实际的登录窗口
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from login_window import LoginWindow

def test_login_window():
    """测试登录窗口"""
    print("=" * 50)
    print("测试实际登录窗口")
    print("=" * 50)
    
    def on_login_success(args):
        print(f"登录成功！服务器: {args.server}:{args.port}")
        print("这里应该打开主窗口，但我们只是测试登录界面")
    
    try:
        # 创建登录窗口
        login_window = LoginWindow(on_login_success=on_login_success)
        
        print("登录窗口已创建，检查所有组件是否可见...")
        print("特别注意按钮是否显示在窗口底部")
        print("窗口将保持打开状态，请手动测试后关闭")
        
        # 运行登录窗口
        login_window.run()
        
        print("登录窗口已关闭")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_window()
