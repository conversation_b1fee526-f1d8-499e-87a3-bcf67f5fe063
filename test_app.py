#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
发票管理工具桌面应用测试脚本
用于测试各个组件的基本功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager
# from toast import Toast
# from drag_drop import DragDropMixin, check_drag_drop_support

class TestConfigManager(unittest.TestCase):
    """测试配置管理器"""
    
    def setUp(self):
        self.config_manager = ConfigManager("test_config.json")
    
    def tearDown(self):
        # 清理测试文件
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
    
    def test_default_config(self):
        """测试默认配置"""
        config = self.config_manager.load_config()
        self.assertIn("server", config)
        self.assertIn("port", config)
        self.assertIn("username", config)
        self.assertEqual(config["server"], "localhost")
        self.assertEqual(config["port"], "8881")
    
    def test_save_and_load_config(self):
        """测试保存和加载配置"""
        test_config = {
            "server": "test.server.com",
            "port": "9999",
            "username": "testuser",
            "password": "testpass",
            "auto_login": True
        }
        
        # 保存配置
        result = self.config_manager.save_config(test_config)
        self.assertTrue(result)
        
        # 加载配置
        loaded_config = self.config_manager.load_config()
        self.assertEqual(loaded_config["server"], "test.server.com")
        self.assertEqual(loaded_config["port"], "9999")
        self.assertEqual(loaded_config["username"], "testuser")
    
    def test_login_config(self):
        """测试登录配置"""
        # 保存登录配置
        result = self.config_manager.save_login_config(
            "example.com", "8080", "admin", "secret", True
        )
        self.assertTrue(result)
        
        # 获取登录配置
        login_config = self.config_manager.get_login_config()
        self.assertEqual(login_config["server"], "example.com")
        self.assertEqual(login_config["port"], "8080")
        self.assertEqual(login_config["auto_login"], True)

class TestDragDrop(unittest.TestCase):
    """测试拖拽功能"""

    def test_drag_drop_support(self):
        """测试拖拽支持检查"""
        # 简化测试，不依赖GUI
        try:
            import tkinterdnd2
            supported = True
            message = "支持高级拖拽功能"
        except ImportError:
            supported = False
            message = "使用简化拖拽功能"

        self.assertIsInstance(supported, bool)
        self.assertIsInstance(message, str)
        print(f"拖拽支持状态: {message}")

def test_gui_components():
    """测试GUI组件（需要显示环境）"""
    try:
        import tkinter as tk

        # 测试Toast组件
        print("测试Toast组件...")
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口

        from toast import Toast
        toast_obj = Toast(root)

        # 这里只是创建对象，不实际显示
        print("✓ Toast组件创建成功")

        root.destroy()

    except Exception as e:
        print(f"GUI组件测试跳过（需要GUI环境）: {e}")

def run_basic_tests():
    """运行基本测试"""
    print("=" * 50)
    print("发票管理工具桌面应用 - 基本功能测试")
    print("=" * 50)
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 测试GUI组件
    print("\n测试GUI组件...")
    test_gui_components()
    
    print("\n测试完成！")

if __name__ == "__main__":
    run_basic_tests()
