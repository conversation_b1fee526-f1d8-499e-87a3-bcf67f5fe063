#!/bin/bash

# tkinter自动安装脚本
# 适用于macOS + pyenv环境

set -e  # 遇到错误立即退出

echo "======================================"
echo "tkinter 自动安装脚本"
echo "======================================"

# 检查操作系统
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "错误：此脚本仅适用于macOS"
    exit 1
fi

# 检查Homebrew
if ! command -v brew &> /dev/null; then
    echo "错误：未找到Homebrew，请先安装Homebrew"
    echo "安装命令：/bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

# 检查pyenv
if ! command -v pyenv &> /dev/null; then
    echo "错误：未找到pyenv"
    exit 1
fi

echo "✓ 环境检查通过"

# 获取当前Python版本
PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
echo "当前Python版本: $PYTHON_VERSION"

# 检查当前tkinter状态
echo "检查当前tkinter状态..."
if python -c "import tkinter" 2>/dev/null; then
    echo "✓ tkinter已经可用，无需安装"
    exit 0
else
    echo "✗ tkinter不可用，开始安装..."
fi

# 安装tcl-tk
echo "安装tcl-tk依赖..."
if brew list tcl-tk &>/dev/null; then
    echo "✓ tcl-tk已安装"
else
    echo "正在安装tcl-tk..."
    brew install tcl-tk
    echo "✓ tcl-tk安装完成"
fi

# 设置环境变量
echo "设置环境变量..."
export PATH="/usr/local/opt/tcl-tk/bin:$PATH"
export LDFLAGS="-L/usr/local/opt/tcl-tk/lib"
export CPPFLAGS="-I/usr/local/opt/tcl-tk/include"
export PKG_CONFIG_PATH="/usr/local/opt/tcl-tk/lib/pkgconfig"

# 检查是否需要重新安装Python
echo "检查Python安装..."
PYTHON_PATH=$(which python)
if [[ $PYTHON_PATH == *"pyenv"* ]]; then
    echo "检测到pyenv管理的Python，需要重新编译以支持tkinter"
    
    # 备份当前环境
    echo "备份当前pip包列表..."
    pip freeze > requirements_backup.txt
    echo "✓ 包列表已保存到 requirements_backup.txt"
    
    # 询问用户是否继续
    read -p "是否继续重新安装Python $PYTHON_VERSION？这将删除当前环境的所有包 (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 1
    fi
    
    # 重新安装Python
    echo "重新安装Python $PYTHON_VERSION..."
    pyenv uninstall -f $PYTHON_VERSION
    
    # 使用环境变量重新安装
    env PATH="/usr/local/opt/tcl-tk/bin:$PATH" \
        LDFLAGS="-L/usr/local/opt/tcl-tk/lib" \
        CPPFLAGS="-I/usr/local/opt/tcl-tk/include" \
        PKG_CONFIG_PATH="/usr/local/opt/tcl-tk/lib/pkgconfig" \
        pyenv install $PYTHON_VERSION
    
    echo "✓ Python重新安装完成"
    
    # 恢复pip包
    echo "恢复pip包..."
    pip install -r requirements_backup.txt
    echo "✓ pip包恢复完成"
    
else
    echo "使用系统Python，尝试其他解决方案..."
fi

# 验证安装
echo "验证tkinter安装..."
if python -c "import tkinter; print('tkinter验证成功')" 2>/dev/null; then
    echo "✓ tkinter安装成功！"
    
    # 运行简单测试
    echo "运行GUI测试..."
    python -c "
import tkinter as tk
import threading
import time

def close_window(root):
    time.sleep(2)
    root.quit()

root = tk.Tk()
root.title('tkinter安装测试')
root.geometry('300x100')
label = tk.Label(root, text='tkinter安装成功！\\n2秒后自动关闭')
label.pack(expand=True)

# 2秒后自动关闭
threading.Thread(target=close_window, args=(root,), daemon=True).start()
root.mainloop()
"
    
    echo "✓ GUI测试完成"
    
    # 清理备份文件
    if [ -f "requirements_backup.txt" ]; then
        rm requirements_backup.txt
        echo "✓ 清理备份文件"
    fi
    
    echo ""
    echo "======================================"
    echo "安装完成！现在可以运行发票应用："
    echo "python invoiceApp.py"
    echo "======================================"
    
else
    echo "✗ tkinter安装失败"
    echo "请尝试手动安装或使用其他Python环境"
    exit 1
fi
