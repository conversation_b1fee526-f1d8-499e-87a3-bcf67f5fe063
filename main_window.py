import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from config_manager import ConfigManager
from toast import Toast
import apiUtil
from glob import glob
from shutil import copyfile

class MainWindow:
    """主窗口"""
    
    def __init__(self, args):
        self.args = args  # 登录时的参数对象
        self.config_manager = ConfigManager()

        # 记住上次选择的目录（用于添加文件/目录操作）
        self.last_browse_dir = "."

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("发票管理工具 - 主界面")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 居中显示窗口
        self._center_window()
        
        # 创建Toast组件
        self.toast = Toast(self.root)
        
        # 创建界面
        self._create_widgets()
        
        # 加载配置
        self._load_config()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # 初始更新列表显示状态
        self._update_list_display()
    
    def _center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="发票管理工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 重命名文件格式定义
        format_frame = ttk.LabelFrame(main_frame, text="重命名文件名格式定义", padding="10")
        format_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.format_var = tk.StringVar()
        self.format_entry = ttk.Entry(format_frame, textvariable=self.format_var, width=80)
        self.format_entry.pack(fill=tk.X)
        # 直接插入默认值
        self.format_entry.insert(0, "{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}")
        
        # 格式说明
        format_help = ttk.Label(format_frame, 
            text="支持字段：{invoicingDate}(开票日期), {invoiceNo}(发票号码), {amountIncludingTax}(含税金额), "
                 "{tax}(税费), {buyerName}(购买方名称), {buyerId}(购买方税号), {sellerName}(销售方名称), {sellerId}(销售方税号)",
            font=("Arial", 8), foreground="gray")
        format_help.pack(anchor=tk.W, pady=(5, 0))
        
        # 重命名文件保存目录
        output_frame = ttk.LabelFrame(main_frame, text="文件保存目录", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        output_inner_frame = ttk.Frame(output_frame)
        output_inner_frame.pack(fill=tk.X)
        
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_inner_frame, textvariable=self.output_var)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        # 插入格式化后的默认值（绝对路径）
        default_output = self._format_path_for_display("./output")
        self.output_entry.insert(0, default_output)
        
        browse_button = ttk.Button(output_inner_frame, text="浏览", command=self._browse_output_dir)
        browse_button.pack(side=tk.RIGHT)
        
        # 发票PDF文件或目录
        files_frame = ttk.LabelFrame(main_frame, text="发票PDF文件或目录", padding="10")
        files_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 文件列表框架
        list_frame = ttk.Frame(files_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建提示标签（当列表为空时显示）
        self.empty_hint_label = ttk.Label(list_frame,
                                         text="点击下方按钮添加文件或目录",
                                         font=("Arial", 10),
                                         foreground="gray",
                                         anchor="center")

        # 创建Treeview来显示文件列表
        self.files_tree = ttk.Treeview(list_frame, columns=("name", "type", "path"), show="tree headings", height=10)
        self.files_tree.heading("#0", text="序号")
        self.files_tree.heading("name", text="名称")
        self.files_tree.heading("type", text="类型")
        self.files_tree.heading("path", text="路径")

        self.files_tree.column("#0", width=50, anchor="center")    # 序号列居中显示
        self.files_tree.column("name", width=180)                 # 名称列
        self.files_tree.column("type", width=80, anchor="center")  # 类型列居中显示
        self.files_tree.column("path", width=370)
        
        # 滚动条
        self.scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=self.scrollbar.set)
        
        # 初始时显示提示标签
        self.empty_hint_label.pack(fill=tk.BOTH, expand=True)

        # Treeview和滚动条初始时隐藏
        # self.files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        # scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 文件操作按钮框架
        file_buttons_frame = ttk.Frame(files_frame)
        file_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        add_button = ttk.Button(file_buttons_frame, text="添加文件或目录", command=self._add_file_or_directory)
        add_button.pack(side=tk.LEFT, padx=(0, 10))
        
        remove_button = ttk.Button(file_buttons_frame, text="移除选中", command=self._remove_selected)
        remove_button.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_button = ttk.Button(file_buttons_frame, text="清空列表", command=self._clear_list)
        clear_button.pack(side=tk.LEFT)
        
        # 主操作按钮框架
        main_buttons_frame = ttk.Frame(main_frame)
        main_buttons_frame.pack(pady=(15, 0))

        # 重命名按钮 - 更大尺寸
        self.rename_button = ttk.Button(main_buttons_frame, text="重命名", command=self._on_rename_click,
                                      width=15)
        self.rename_button.grid(row=0, column=0, padx=(0, 20), ipadx=20, ipady=8)

        # 退出按钮 - 更大尺寸
        exit_button = ttk.Button(main_buttons_frame, text="退出", command=self._on_exit_click,
                               width=15)
        exit_button.grid(row=0, column=1, ipadx=20, ipady=8)
        




    def _load_config(self):
        """加载配置"""
        config = self.config_manager.get_main_config()

        # 清空并重新设置Entry控件的值
        self.format_entry.delete(0, tk.END)
        self.format_entry.insert(0, config["format"])

        # 格式化输出路径用于显示（显示绝对路径）
        display_output = self._format_path_for_display(config["output"])
        self.output_entry.delete(0, tk.END)
        self.output_entry.insert(0, display_output)

        # 同时更新变量（保存原始配置路径）
        self.format_var.set(config["format"])
        self.output_var.set(config["output"])

        # 加载记忆目录
        self.last_browse_dir = config["last_browse_dir"]

        # 强制刷新界面
        self.root.update_idletasks()

        # 加载文件列表
        for file_path in config["invoice_files"]:
            if file_path.strip():  # 只添加非空路径
                self._add_file_to_tree(file_path)

        # 更新列表显示状态
        self._update_list_display()

    def _save_config(self):
        """保存配置"""
        # 获取文件列表
        invoice_files = []
        for item in self.files_tree.get_children():
            values = self.files_tree.item(item)["values"]
            if len(values) >= 3:  # 确保有足够的列
                path = values[2]  # 路径现在在第3列（索引2）
                invoice_files.append(path)

        self.config_manager.save_main_config(
            self.format_var.get(),
            self.output_var.get(),
            invoice_files,
            self.last_browse_dir
        )

    def _format_path_for_display(self, path):
        """格式化路径用于显示，始终显示绝对路径"""
        if not path:
            return path

        try:
            # 转换为绝对路径
            abs_path = os.path.abspath(path)
            return abs_path
        except (ValueError, OSError):
            # 如果转换失败，返回原路径
            return path

    def _get_actual_path(self, display_path):
        """从显示路径获取实际的绝对路径"""
        if not display_path:
            return display_path

        try:
            # 如果是相对路径，转换为绝对路径
            if not os.path.isabs(display_path):
                return os.path.abspath(display_path)
            else:
                return display_path
        except (ValueError, OSError):
            return display_path

    def _update_list_display(self):
        """更新列表显示状态：空列表显示提示，非空列表显示树形控件"""
        has_items = len(self.files_tree.get_children()) > 0

        if has_items:
            # 有文件时显示树形控件和滚动条，隐藏提示
            self.empty_hint_label.pack_forget()
            self.files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        else:
            # 无文件时显示提示，隐藏树形控件和滚动条
            self.files_tree.pack_forget()
            self.scrollbar.pack_forget()
            self.empty_hint_label.pack(fill=tk.BOTH, expand=True)

    def _browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择重命名文件保存目录",
            initialdir=self.output_var.get() if self.output_var.get() else "."
        )
        if directory:
            # 格式化路径用于显示（显示绝对路径）
            display_path = self._format_path_for_display(directory)

            # 更新Entry控件显示绝对路径
            self.output_entry.delete(0, tk.END)
            self.output_entry.insert(0, display_path)

            # StringVar保存用户选择的原始路径（可能是相对路径）
            self.output_var.set(directory)
            self._save_config()

    def _add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")],
            initialdir=self.last_browse_dir
        )
        for file_path in files:
            self._add_file_to_tree(file_path)

        if files:
            # 更新记忆目录为第一个选择文件的目录
            import os
            self.last_browse_dir = os.path.dirname(files[0])
            self._save_config()
            # 更新列表显示状态
            self._update_list_display()

    def _add_directory(self):
        """扫描目录中的PDF文件"""
        directory = filedialog.askdirectory(
            title="选择要扫描PDF文件的目录",
            initialdir=self.last_browse_dir
        )
        if directory:
            # 更新记忆目录
            self.last_browse_dir = directory

            # 扫描目录中的所有PDF文件
            pdf_files = self._scan_pdf_files(directory)

            if pdf_files:
                # 添加找到的PDF文件到列表
                for pdf_file in pdf_files:
                    self._add_file_to_tree(pdf_file)

                self._save_config()
                # 更新列表显示状态
                self._update_list_display()

                # 确保滚动到底部显示最新添加的文件
                children = self.files_tree.get_children()
                if children:
                    self.files_tree.see(children[-1])

                # 显示扫描结果
                self.toast.show_success(f"已扫描并添加 {len(pdf_files)} 个PDF文件")
            else:
                # 没有找到PDF文件
                self.toast.show_info("在选择的目录中未找到PDF文件")

    def _add_file_or_directory(self):
        """添加文件或目录的统一入口"""
        from tkinter import filedialog
        import tkinter.messagebox as messagebox

        # 使用简单的选择对话框让用户选择操作类型
        choice = messagebox.askyesno(
            "添加文件或目录",
            "请选择操作类型：\n\n"
            "点击'是'选择PDF文件（可多选）\n"
            "点击'否'选择目录扫描PDF文件",
            icon='question'
        )

        if choice:
            # 用户选择添加文件
            files = filedialog.askopenfilenames(
                title="选择PDF文件（可多选）",
                initialdir=self.last_browse_dir,
                filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
            )

            if files:
                # 更新记忆目录
                self.last_browse_dir = os.path.dirname(files[0])

                # 添加选中的文件
                for file_path in files:
                    self._add_file_to_tree(file_path)

                self._save_config()
                self._update_list_display()

                # 显示添加结果
                self.toast.show_success(f"已添加 {len(files)} 个文件")
        else:
            # 用户选择扫描目录
            directory = filedialog.askdirectory(
                title="选择要扫描PDF文件的目录",
                initialdir=self.last_browse_dir
            )

            if directory:
                # 用户选择了目录，执行目录扫描
                self.last_browse_dir = directory

                # 扫描目录中的所有PDF文件
                pdf_files = self._scan_pdf_files(directory)

                if pdf_files:
                    # 添加找到的PDF文件到列表
                    for pdf_file in pdf_files:
                        self._add_file_to_tree(pdf_file)

                    self._save_config()
                    # 更新列表显示状态
                    self._update_list_display()

                    # 确保滚动到底部显示最新添加的文件
                    children = self.files_tree.get_children()
                    if children:
                        self.files_tree.see(children[-1])

                    # 显示扫描结果
                    self.toast.show_success(f"已扫描并添加 {len(pdf_files)} 个PDF文件")
                else:
                    # 没有找到PDF文件
                    self.toast.show_info("在选择的目录中未找到PDF文件")

    def _scan_pdf_files(self, directory):
        """扫描目录及其子目录中的所有PDF文件"""
        pdf_files = []

        try:
            # 使用os.walk递归遍历目录
            for root, _, files in os.walk(directory):
                for file in files:
                    # 检查文件扩展名是否为PDF（不区分大小写）
                    if file.lower().endswith('.pdf'):
                        full_path = os.path.join(root, file)
                        pdf_files.append(full_path)

            # 按文件名排序
            pdf_files.sort()

        except Exception as e:
            print(f"扫描目录时出错: {e}")
            self.toast.show_error(f"扫描目录时出错: {str(e)}")

        return pdf_files

    def _add_file_to_tree(self, file_path):
        """添加文件到树形控件"""
        if not file_path:
            return

        # 确定类型和名称
        if os.path.isfile(file_path):
            file_type = "文件"
            name = os.path.basename(file_path)
        elif os.path.isdir(file_path):
            file_type = "目录"
            name = os.path.basename(file_path)
        else:
            file_type = "未知"
            name = os.path.basename(file_path)

        # 检查是否已存在相同路径的项，如果存在则移除（保留最新添加的）
        items_to_remove = []
        for item in self.files_tree.get_children():
            item_values = self.files_tree.item(item)["values"]
            if len(item_values) >= 3 and item_values[2] == file_path:  # 路径在第3列（索引2）
                items_to_remove.append(item)

        # 移除重复项
        for item in items_to_remove:
            self.files_tree.delete(item)

        # 添加新项到树形控件
        # #0列显示序号，values包含(名称, 类型, 路径)
        new_item = self.files_tree.insert("", tk.END, text="0", values=(name, file_type, file_path))

        # 更新所有项的序号
        self._update_sequence_numbers()

        # 滚动到新添加的项目（底部）
        self.files_tree.see(new_item)

    def _update_sequence_numbers(self):
        """更新所有项的序号"""
        children = self.files_tree.get_children()
        for i, item in enumerate(children, 1):
            # 更新序号到#0列（text属性）
            self.files_tree.item(item, text=str(i))

    def _remove_selected(self):
        """移除选中的项"""
        selected_items = self.files_tree.selection()
        for item in selected_items:
            self.files_tree.delete(item)

        if selected_items:
            # 更新序号
            self._update_sequence_numbers()
            self._save_config()
            # 更新列表显示状态
            self._update_list_display()

    def _clear_list(self):
        """清空列表"""
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)
        # 清空后不需要更新序号，因为没有项了
        self._save_config()
        # 更新列表显示状态
        self._update_list_display()

    def _on_rename_click(self):
        """重命名按钮点击事件"""
        # 验证输入
        if not self.format_var.get().strip():
            self.toast.show_error("请输入重命名文件格式定义")
            return

        if not self.output_var.get().strip():
            self.toast.show_error("请选择重命名文件保存目录")
            return

        # 检查是否有文件
        if not self.files_tree.get_children():
            self.toast.show_error("请添加要处理的PDF文件或目录")
            return

        # 保存当前配置
        self._save_config()

        # 禁用重命名按钮
        self.rename_button.config(state=tk.DISABLED, text="重命名中...")

        # 在新线程中执行重命名
        threading.Thread(target=self._do_rename, daemon=True).start()

    def _do_rename(self):
        """执行重命名操作"""
        try:
            # 获取文件列表
            invoice_files = []
            for item in self.files_tree.get_children():
                path = self.files_tree.item(item)["values"][1]
                invoice_files.append(path)

            # 设置参数
            self.args.format = self.format_var.get()
            self.args.output = self.output_var.get()
            self.args.invoice = invoice_files

            # 调用重命名函数
            success_count, fail_count = self._invoice_rename(self.args)

            # 在主线程中显示结果
            self.root.after(0, lambda: self._handle_rename_result(success_count, fail_count))

        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self._handle_rename_error(str(e)))

    def _invoice_rename(self, args):
        """重命名发票文件（基于原有逻辑）"""
        argsFormat = args.format
        sources = []
        success_count = 0
        fail_count = 0

        # 处理每个源文件或目录
        for source in args.invoice:
            if os.path.isdir(source):
                source = "{}/*.*".format(source)
            files = glob(source)

            # 创建输出目录
            resultPath = "{}/".format(args.output)
            if not os.path.exists(resultPath):
                os.makedirs(resultPath)

            failPath = "{}fail/".format(resultPath)
            if not os.path.exists(failPath):
                os.makedirs(failPath)

            # 处理每个文件
            for f in files:
                if not self._has(sources, f):
                    sources.append(f)
                    if (os.path.exists(f)) and (not os.path.isdir(f)) and f.lower().endswith('.pdf'):
                        try:
                            # 上传文件并获取发票信息
                            playload = {
                                'operations': '{"query":"mutation insertInvoiceInfo($uploadFile: Upload!) {insertInvoiceInfo(file: $uploadFile) {ok code message data {id fileMd5 invoicingDate invoiceNo amountIncludingTax tax buyerName buyerId sellerName sellerId}}}","variables":{"uploadFile":null}}',
                                'map': '{"0": ["variables.uploadFile"]}'
                            }
                            files_upload = [
                                ('0', (os.path.basename(f), open(f, 'rb'), 'application/pdf'))
                            ]
                            result = apiUtil.graphqlUpload(args, playload, files_upload).get('data').get('insertInvoiceInfo')

                            if (result.get('code') == 200 and result.get('data') != None and
                                result.get('data').get('invoiceNo') != None and
                                result.get('data').get('invoicingDate') != None and
                                result.get('data').get('amountIncludingTax') != None):

                                # 生成新文件名
                                newFilenamePart = argsFormat.format_map(result.get('data'))
                                newFilenameBase = "{}{}".format(resultPath, newFilenamePart)
                                index = 0
                                newFilename = "{}.pdf".format(newFilenameBase)
                                while (os.path.exists(newFilename)):
                                    index += 1
                                    newFilename = "{}({}).pdf".format(newFilenameBase, index)

                                # 复制文件
                                copyfile(f, newFilename)
                                success_count += 1
                            else:
                                # 处理失败的文件
                                newFailFilenameBase = "{}{}".format(failPath, os.path.basename(f))
                                index = 0
                                newFailFilename = "{}".format(newFailFilenameBase)
                                while (os.path.exists(newFailFilename)):
                                    index += 1
                                    newFailFilename = "{}({})".format(newFailFilenameBase, index)

                                copyfile(f, newFailFilename)
                                fail_count += 1
                        except Exception as e:
                            fail_count += 1
                            print(f"处理文件 {f} 时出错: {e}")

        return success_count, fail_count

    def _has(self, s, v):
        """检查列表中是否包含某个值"""
        for x in s:
            if (x == v):
                return True
        return False

    def _handle_rename_result(self, success_count, fail_count):
        """处理重命名结果"""
        # 恢复重命名按钮
        self.rename_button.config(state=tk.NORMAL, text="重命名")

        # 显示结果
        if success_count > 0 and fail_count == 0:
            self.toast.show_success(f"重命名完成！成功处理 {success_count} 个文件")
        elif success_count > 0 and fail_count > 0:
            self.toast.show_warning(f"重命名完成！成功 {success_count} 个，失败 {fail_count} 个")
        elif success_count == 0 and fail_count > 0:
            self.toast.show_error(f"重命名失败！{fail_count} 个文件处理失败")
        else:
            self.toast.show_info("没有找到可处理的PDF文件")

    def _handle_rename_error(self, error_msg):
        """处理重命名错误"""
        # 恢复重命名按钮
        self.rename_button.config(state=tk.NORMAL, text="重命名")

        # 显示错误信息
        self.toast.show_error(f"重命名失败: {error_msg}")

    def _on_exit_click(self):
        """退出按钮点击事件"""
        self._save_config()
        self.root.quit()
        self.root.destroy()

    def _on_closing(self):
        """窗口关闭事件"""
        self._save_config()
        self.root.destroy()

    def run(self):
        """运行主窗口"""
        self.root.mainloop()
