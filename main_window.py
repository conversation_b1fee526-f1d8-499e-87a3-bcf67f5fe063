import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from config_manager import ConfigManager
from toast import Toast
from drag_drop import DragDropMixin, show_drag_drop_info
import apiUtil
from glob import glob
from shutil import copyfile

class MainWindow(DragDropMixin):
    """主窗口"""
    
    def __init__(self, args):
        self.args = args  # 登录时的参数对象
        self.config_manager = ConfigManager()
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("发票管理工具 - 主界面")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 居中显示窗口
        self._center_window()
        
        # 创建Toast组件
        self.toast = Toast(self.root)
        
        # 创建界面
        self._create_widgets()
        
        # 加载配置
        self._load_config()
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="发票管理工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 重命名文件格式定义
        format_frame = ttk.LabelFrame(main_frame, text="重命名文件名格式定义", padding="10")
        format_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.format_var = tk.StringVar()
        self.format_entry = ttk.Entry(format_frame, textvariable=self.format_var, width=80)
        self.format_entry.pack(fill=tk.X)
        # 直接插入默认值
        self.format_entry.insert(0, "{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}")
        
        # 格式说明
        format_help = ttk.Label(format_frame, 
            text="支持字段：{invoicingDate}(开票日期), {invoiceNo}(发票号码), {amountIncludingTax}(含税金额), "
                 "{tax}(税费), {buyerName}(购买方名称), {buyerId}(购买方税号), {sellerName}(销售方名称), {sellerId}(销售方税号)",
            font=("Arial", 8), foreground="gray")
        format_help.pack(anchor=tk.W, pady=(5, 0))
        
        # 重命名文件保存目录
        output_frame = ttk.LabelFrame(main_frame, text="文件保存目录", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        output_inner_frame = ttk.Frame(output_frame)
        output_inner_frame.pack(fill=tk.X)
        
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_inner_frame, textvariable=self.output_var)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        # 直接插入默认值
        self.output_entry.insert(0, "./output")
        
        browse_button = ttk.Button(output_inner_frame, text="浏览", command=self._browse_output_dir)
        browse_button.pack(side=tk.RIGHT)
        
        # 发票PDF文件或目录
        files_frame = ttk.LabelFrame(main_frame, text="发票PDF文件或目录", padding="10")
        files_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 文件列表框架
        list_frame = ttk.Frame(files_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview来显示文件列表
        self.files_tree = ttk.Treeview(list_frame, columns=("type", "path"), show="tree headings", height=10)
        self.files_tree.heading("#0", text="名称")
        self.files_tree.heading("type", text="类型")
        self.files_tree.heading("path", text="路径")
        
        self.files_tree.column("#0", width=200)
        self.files_tree.column("type", width=80)
        self.files_tree.column("path", width=400)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=scrollbar.set)
        
        self.files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 文件操作按钮框架
        file_buttons_frame = ttk.Frame(files_frame)
        file_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        add_file_button = ttk.Button(file_buttons_frame, text="添加文件", command=self._add_files)
        add_file_button.pack(side=tk.LEFT, padx=(0, 10))
        
        add_dir_button = ttk.Button(file_buttons_frame, text="添加目录", command=self._add_directory)
        add_dir_button.pack(side=tk.LEFT, padx=(0, 10))
        
        remove_button = ttk.Button(file_buttons_frame, text="移除选中", command=self._remove_selected)
        remove_button.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_button = ttk.Button(file_buttons_frame, text="清空列表", command=self._clear_list)
        clear_button.pack(side=tk.LEFT)
        
        # 主操作按钮框架
        main_buttons_frame = ttk.Frame(main_frame)
        main_buttons_frame.pack(pady=(15, 0))

        # 重命名按钮 - 更大尺寸
        self.rename_button = ttk.Button(main_buttons_frame, text="重命名", command=self._on_rename_click,
                                      width=15)
        self.rename_button.grid(row=0, column=0, padx=(0, 20), ipadx=20, ipady=8)

        # 退出按钮 - 更大尺寸
        exit_button = ttk.Button(main_buttons_frame, text="退出", command=self._on_exit_click,
                               width=15)
        exit_button.grid(row=0, column=1, ipadx=20, ipady=8)
        
        # 启用拖拽功能
        self._setup_drag_drop()

        # 显示拖拽功能信息
        show_drag_drop_info()

    def _setup_drag_drop(self):
        """设置拖拽功能"""
        # 使用DragDropMixin的功能
        self.setup_drag_drop(self.files_tree, self._on_files_dropped)

    def _on_files_dropped(self, file_paths):
        """处理拖拽的文件"""
        for file_path in file_paths:
            self._add_file_to_tree(file_path)

        if file_paths:
            self._save_config()
            self.toast.show_success(f"已添加 {len(file_paths)} 个文件/目录")

    def _load_config(self):
        """加载配置"""
        config = self.config_manager.get_main_config()

        self.format_var.set(config["format"])
        self.output_var.set(config["output"])

        # 强制刷新界面
        self.root.update_idletasks()

        # 加载文件列表
        for file_path in config["invoice_files"]:
            if file_path.strip():  # 只添加非空路径
                self._add_file_to_tree(file_path)

    def _save_config(self):
        """保存配置"""
        # 获取文件列表
        invoice_files = []
        for item in self.files_tree.get_children():
            path = self.files_tree.item(item)["values"][1]
            invoice_files.append(path)

        self.config_manager.save_main_config(
            self.format_var.get(),
            self.output_var.get(),
            invoice_files
        )

    def _browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择重命名文件保存目录",
            initialdir=self.output_var.get() if self.output_var.get() else "."
        )
        if directory:
            self.output_var.set(directory)
            self._save_config()

    def _add_files(self):
        """添加文件"""
        files = filedialog.askopenfilenames(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        for file_path in files:
            self._add_file_to_tree(file_path)

        if files:
            self._save_config()

    def _add_directory(self):
        """添加目录"""
        directory = filedialog.askdirectory(title="选择包含PDF文件的目录")
        if directory:
            self._add_file_to_tree(directory)
            self._save_config()

    def _add_file_to_tree(self, file_path):
        """添加文件到树形控件"""
        if not file_path:
            return

        # 检查是否已存在
        for item in self.files_tree.get_children():
            if self.files_tree.item(item)["values"][1] == file_path:
                return  # 已存在，不重复添加

        # 确定类型和名称
        if os.path.isfile(file_path):
            file_type = "文件"
            name = os.path.basename(file_path)
        elif os.path.isdir(file_path):
            file_type = "目录"
            name = os.path.basename(file_path)
        else:
            file_type = "未知"
            name = os.path.basename(file_path)

        # 添加到树形控件
        self.files_tree.insert("", tk.END, text=name, values=(file_type, file_path))

    def _remove_selected(self):
        """移除选中的项"""
        selected_items = self.files_tree.selection()
        for item in selected_items:
            self.files_tree.delete(item)

        if selected_items:
            self._save_config()

    def _clear_list(self):
        """清空列表"""
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)
        self._save_config()

    def _on_rename_click(self):
        """重命名按钮点击事件"""
        # 验证输入
        if not self.format_var.get().strip():
            self.toast.show_error("请输入重命名文件格式定义")
            return

        if not self.output_var.get().strip():
            self.toast.show_error("请选择重命名文件保存目录")
            return

        # 检查是否有文件
        if not self.files_tree.get_children():
            self.toast.show_error("请添加要处理的PDF文件或目录")
            return

        # 保存当前配置
        self._save_config()

        # 禁用重命名按钮
        self.rename_button.config(state=tk.DISABLED, text="重命名中...")

        # 在新线程中执行重命名
        threading.Thread(target=self._do_rename, daemon=True).start()

    def _do_rename(self):
        """执行重命名操作"""
        try:
            # 获取文件列表
            invoice_files = []
            for item in self.files_tree.get_children():
                path = self.files_tree.item(item)["values"][1]
                invoice_files.append(path)

            # 设置参数
            self.args.format = self.format_var.get()
            self.args.output = self.output_var.get()
            self.args.invoice = invoice_files

            # 调用重命名函数
            success_count, fail_count = self._invoice_rename(self.args)

            # 在主线程中显示结果
            self.root.after(0, lambda: self._handle_rename_result(success_count, fail_count))

        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self._handle_rename_error(str(e)))

    def _invoice_rename(self, args):
        """重命名发票文件（基于原有逻辑）"""
        argsFormat = args.format
        sources = []
        success_count = 0
        fail_count = 0

        # 处理每个源文件或目录
        for source in args.invoice:
            if os.path.isdir(source):
                source = "{}/*.*".format(source)
            files = glob(source)

            # 创建输出目录
            resultPath = "{}/".format(args.output)
            if not os.path.exists(resultPath):
                os.makedirs(resultPath)

            failPath = "{}fail/".format(resultPath)
            if not os.path.exists(failPath):
                os.makedirs(failPath)

            # 处理每个文件
            for f in files:
                if not self._has(sources, f):
                    sources.append(f)
                    if (os.path.exists(f)) and (not os.path.isdir(f)) and f.lower().endswith('.pdf'):
                        try:
                            # 上传文件并获取发票信息
                            playload = {
                                'operations': '{"query":"mutation insertInvoiceInfo($uploadFile: Upload!) {insertInvoiceInfo(file: $uploadFile) {ok code message data {id fileMd5 invoicingDate invoiceNo amountIncludingTax tax buyerName buyerId sellerName sellerId}}}","variables":{"uploadFile":null}}',
                                'map': '{"0": ["variables.uploadFile"]}'
                            }
                            files_upload = [
                                ('0', (os.path.basename(f), open(f, 'rb'), 'application/pdf'))
                            ]
                            result = apiUtil.graphqlUpload(args, playload, files_upload).get('data').get('insertInvoiceInfo')

                            if (result.get('code') == 200 and result.get('data') != None and
                                result.get('data').get('invoiceNo') != None and
                                result.get('data').get('invoicingDate') != None and
                                result.get('data').get('amountIncludingTax') != None):

                                # 生成新文件名
                                newFilenamePart = argsFormat.format_map(result.get('data'))
                                newFilenameBase = "{}{}".format(resultPath, newFilenamePart)
                                index = 0
                                newFilename = "{}.pdf".format(newFilenameBase)
                                while (os.path.exists(newFilename)):
                                    index += 1
                                    newFilename = "{}({}).pdf".format(newFilenameBase, index)

                                # 复制文件
                                copyfile(f, newFilename)
                                success_count += 1
                            else:
                                # 处理失败的文件
                                newFailFilenameBase = "{}{}".format(failPath, os.path.basename(f))
                                index = 0
                                newFailFilename = "{}".format(newFailFilenameBase)
                                while (os.path.exists(newFailFilename)):
                                    index += 1
                                    newFailFilename = "{}({})".format(newFailFilenameBase, index)

                                copyfile(f, newFailFilename)
                                fail_count += 1
                        except Exception as e:
                            fail_count += 1
                            print(f"处理文件 {f} 时出错: {e}")

        return success_count, fail_count

    def _has(self, s, v):
        """检查列表中是否包含某个值"""
        for x in s:
            if (x == v):
                return True
        return False

    def _handle_rename_result(self, success_count, fail_count):
        """处理重命名结果"""
        # 恢复重命名按钮
        self.rename_button.config(state=tk.NORMAL, text="重命名")

        # 显示结果
        if success_count > 0 and fail_count == 0:
            self.toast.show_success(f"重命名完成！成功处理 {success_count} 个文件")
        elif success_count > 0 and fail_count > 0:
            self.toast.show_warning(f"重命名完成！成功 {success_count} 个，失败 {fail_count} 个")
        elif success_count == 0 and fail_count > 0:
            self.toast.show_error(f"重命名失败！{fail_count} 个文件处理失败")
        else:
            self.toast.show_info("没有找到可处理的PDF文件")

    def _handle_rename_error(self, error_msg):
        """处理重命名错误"""
        # 恢复重命名按钮
        self.rename_button.config(state=tk.NORMAL, text="重命名")

        # 显示错误信息
        self.toast.show_error(f"重命名失败: {error_msg}")

    def _on_exit_click(self):
        """退出按钮点击事件"""
        self._save_config()
        self.root.quit()
        self.root.destroy()

    def _on_closing(self):
        """窗口关闭事件"""
        self._save_config()
        self.root.destroy()

    def run(self):
        """运行主窗口"""
        self.root.mainloop()
