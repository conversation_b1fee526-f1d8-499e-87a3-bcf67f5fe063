# 发票管理工具 - 桌面应用版本

## 概述

发票管理工具桌面应用（invoiceApp.py）是基于原有命令行工具（invoice.py）开发的图形界面版本，提供了更加友好的可视化操作界面。

## 功能特性

### 1. 登录界面
- **服务器地址**：rayoe服务器的域名或IP地址
- **端口**：服务器端口号
- **用户名**：登录用户名
- **密码**：登录密码
- **自动登录**：勾选后下次启动时自动登录
- **设置保存**：所有设置会自动保存，下次启动时恢复

### 2. 主界面
- **重命名格式定义**：自定义文件重命名格式，支持多种字段
- **保存目录**：指定重命名后文件的保存位置
- **PDF文件管理**：支持添加文件、目录，以及拖拽操作
- **批量处理**：一次性处理多个PDF文件

### 3. 高级功能
- **Toast提示**：操作成功/失败的轻提示反馈
- **拖拽支持**：直接拖拽文件到应用中（需要tkinterdnd2库）
- **配置管理**：自动保存用户设置到配置文件
- **错误处理**：完善的错误提示和异常处理

## 安装要求

### 必需依赖
```bash
pip install requests
```

### 可选依赖（增强功能）
```bash
pip install tkinterdnd2  # 启用完整拖拽功能
```

## 使用方法

### 1. 启动应用
```bash
python invoiceApp.py
```

### 2. 登录设置
1. 输入rayoe服务器地址（默认：localhost）
2. 输入端口号（默认：8881）
3. 输入用户名（默认：admin）
4. 输入密码（默认：123456）
5. 可选择"自动登录"以便下次自动登录
6. 点击"登录"按钮

### 3. 主界面操作
1. **设置重命名格式**：
   - 默认格式：`{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}`
   - 支持的字段：
     - `{invoicingDate}` - 开票日期
     - `{invoiceNo}` - 发票号码
     - `{amountIncludingTax}` - 含税金额
     - `{tax}` - 税费
     - `{buyerName}` - 购买方名称
     - `{buyerId}` - 购买方税号
     - `{sellerName}` - 销售方名称
     - `{sellerId}` - 销售方税号

2. **选择保存目录**：
   - 点击"浏览"按钮选择目录
   - 默认保存到 `./output` 目录

3. **添加PDF文件**：
   - 点击"添加文件"选择单个或多个PDF文件
   - 点击"添加目录"选择包含PDF文件的目录
   - 直接拖拽文件或目录到文件列表中（需要tkinterdnd2）
   - 右键点击文件列表可快速添加文件

4. **执行重命名**：
   - 点击"重命名"按钮开始处理
   - 处理过程中会显示进度提示
   - 完成后会显示成功/失败统计

## 文件结构

```
invoiceApp.py          # 主应用程序
config_manager.py      # 配置管理模块
login_window.py        # 登录界面
main_window.py         # 主界面
toast.py              # Toast提示组件
drag_drop.py          # 拖拽功能支持
test_app.py           # 测试脚本
invoice_config.json   # 配置文件（自动生成）
```

## 配置文件

应用会自动创建 `invoice_config.json` 配置文件，包含：
- 登录信息（服务器、端口、用户名、密码、自动登录设置）
- 主界面设置（重命名格式、输出目录、文件列表）

## 错误处理

### 常见问题
1. **登录失败**：检查服务器地址、端口、用户名和密码
2. **文件处理失败**：失败的文件会保存到 `output/fail/` 目录
3. **拖拽不可用**：安装 `tkinterdnd2` 库或使用右键菜单添加文件

### 日志信息
应用启动时会显示：
- 依赖库检查结果
- 服务器连接状态
- 拖拽功能支持情况

## 测试

### 快速登录功能测试
```bash
python quick_login_test.py
```

### 完整功能测试
```bash
python test_local_server.py
```

### 基本功能测试
```bash
python test_app.py
```

## 与命令行版本的对比

| 功能 | 命令行版本 | 桌面版本 |
|------|------------|----------|
| 用户界面 | 命令行参数 | 图形界面 |
| 配置管理 | 每次输入 | 自动保存 |
| 文件选择 | 通配符 | 可视化选择 |
| 拖拽支持 | 无 | 支持 |
| 进度反馈 | 文本输出 | Toast提示 |
| 错误处理 | 控制台输出 | 图形提示 |

## 技术实现

- **GUI框架**：tkinter（Python标准库）
- **配置管理**：JSON格式配置文件
- **异步处理**：多线程处理文件上传和重命名
- **拖拽支持**：tkinterdnd2库（可选）
- **网络通信**：复用原有的apiUtil模块

## 版本信息

- **版本**：1.0.0
- **兼容性**：与原有命令行工具完全兼容
- **Python版本**：3.6+
