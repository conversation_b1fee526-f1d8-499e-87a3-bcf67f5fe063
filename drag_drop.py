import tkinter as tk
from tkinter import messagebox
import os
import sys

class DragDropMixin:
    """拖拽功能混入类"""
    
    def setup_drag_drop(self, widget, callback):
        """
        设置拖拽功能
        
        Args:
            widget: 目标控件
            callback: 拖拽完成后的回调函数，接收文件路径列表作为参数
        """
        self.drag_callback = callback
        
        # 尝试使用tkinterdnd2库（如果可用）
        try:
            import tkinterdnd2 as tkdnd
            from tkinterdnd2 import DND_FILES
            
            # 设置拖拽目标
            widget.drop_target_register(DND_FILES)
            widget.dnd_bind('<<Drop>>', self._on_drop_files)
            
        except ImportError:
            # 如果没有tkinterdnd2，使用简化的拖拽支持
            self._setup_simple_drag_drop(widget)
    
    def _on_drop_files(self, event):
        """处理文件拖拽事件（tkinterdnd2版本）"""
        files = event.data.split()
        valid_files = []
        
        for file_path in files:
            # 清理路径（移除大括号等）
            file_path = file_path.strip('{}')
            if os.path.exists(file_path):
                valid_files.append(file_path)
        
        if valid_files and self.drag_callback:
            self.drag_callback(valid_files)
    
    def _setup_simple_drag_drop(self, widget):
        """设置简化的拖拽支持"""
        # 绑定右键菜单，提供添加文件的选项
        def show_context_menu(event):
            context_menu = tk.Menu(widget, tearoff=0)
            context_menu.add_command(label="添加文件...", command=self._add_files_dialog)
            context_menu.add_command(label="添加目录...", command=self._add_directory_dialog)
            context_menu.tk_popup(event.x_root, event.y_root)
        
        widget.bind("<Button-3>", show_context_menu)  # 右键
        
        # 添加提示标签
        if hasattr(widget, 'insert'):
            widget.insert("", tk.END, text="右键点击添加文件或目录", values=("提示", ""))
    
    def _add_files_dialog(self):
        """文件选择对话框"""
        from tkinter import filedialog
        files = filedialog.askopenfilenames(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        if files and self.drag_callback:
            self.drag_callback(list(files))
    
    def _add_directory_dialog(self):
        """目录选择对话框"""
        from tkinter import filedialog
        directory = filedialog.askdirectory(title="选择包含PDF文件的目录")
        if directory and self.drag_callback:
            self.drag_callback([directory])

# 检查是否支持高级拖拽功能
def check_drag_drop_support():
    """检查拖拽支持情况"""
    try:
        import tkinterdnd2
        return True, "支持高级拖拽功能"
    except ImportError:
        return False, "使用简化拖拽功能（右键菜单）"

# 安装tkinterdnd2的提示
def show_drag_drop_info():
    """显示拖拽功能信息"""
    supported, message = check_drag_drop_support()
    if not supported:
        print("提示：要启用完整的拖拽功能，请安装tkinterdnd2库：")
        print("pip install tkinterdnd2")
        print("当前使用简化版本：右键点击文件列表可添加文件")
    return supported
