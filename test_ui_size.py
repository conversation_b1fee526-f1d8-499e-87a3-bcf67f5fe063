#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试界面大小和布局
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_login_window_size():
    """测试登录窗口大小"""
    print("测试登录窗口大小...")
    
    root = tk.Tk()
    root.title("发票管理工具 - 登录")
    root.geometry("450x400")
    root.resizable(True, True)
    root.minsize(400, 350)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="30")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="发票管理工具", font=("Arial", 18, "bold"))
    title_label.pack(pady=(0, 30))
    
    # 创建输入字段框架
    fields_frame = ttk.Frame(main_frame)
    fields_frame.pack(fill=tk.X, pady=(0, 20))
    
    # 服务器地址
    server_frame = ttk.Frame(fields_frame)
    server_frame.pack(fill=tk.X, pady=(0, 15))
    ttk.Label(server_frame, text="服务器地址:", font=("Arial", 10)).pack(anchor=tk.W)
    server_var = tk.StringVar(value="localhost")
    server_entry = ttk.Entry(server_frame, textvariable=server_var, font=("Arial", 10))
    server_entry.pack(fill=tk.X, pady=(5, 0))
    
    # 端口
    port_frame = ttk.Frame(fields_frame)
    port_frame.pack(fill=tk.X, pady=(0, 15))
    ttk.Label(port_frame, text="端口:", font=("Arial", 10)).pack(anchor=tk.W)
    port_var = tk.StringVar(value="8881")
    port_entry = ttk.Entry(port_frame, textvariable=port_var, font=("Arial", 10))
    port_entry.pack(fill=tk.X, pady=(5, 0))
    
    # 用户名
    username_frame = ttk.Frame(fields_frame)
    username_frame.pack(fill=tk.X, pady=(0, 15))
    ttk.Label(username_frame, text="用户名:", font=("Arial", 10)).pack(anchor=tk.W)
    username_var = tk.StringVar(value="admin")
    username_entry = ttk.Entry(username_frame, textvariable=username_var, font=("Arial", 10))
    username_entry.pack(fill=tk.X, pady=(5, 0))
    
    # 密码
    password_frame = ttk.Frame(fields_frame)
    password_frame.pack(fill=tk.X, pady=(0, 15))
    ttk.Label(password_frame, text="密码:", font=("Arial", 10)).pack(anchor=tk.W)
    password_var = tk.StringVar(value="123456")
    password_entry = ttk.Entry(password_frame, textvariable=password_var, show="*", font=("Arial", 10))
    password_entry.pack(fill=tk.X, pady=(5, 0))
    
    # 自动登录复选框
    auto_login_var = tk.BooleanVar()
    auto_login_check = ttk.Checkbutton(fields_frame, text="自动登录", variable=auto_login_var)
    auto_login_check.pack(anchor=tk.W, pady=(10, 0))
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))
    
    # 登录按钮
    login_button = ttk.Button(button_frame, text="登录")
    login_button.pack(side=tk.LEFT, padx=(0, 15), ipadx=20, ipady=5)
    
    # 退出按钮
    exit_button = ttk.Button(button_frame, text="退出", command=root.quit)
    exit_button.pack(side=tk.LEFT, ipadx=20, ipady=5)
    
    # 状态栏
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=(20, 0))
    status_label = ttk.Label(status_frame, text="请输入登录信息", 
                           font=("Arial", 9), foreground="gray")
    status_label.pack()
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    print(f"登录窗口大小: {width}x{height}")
    print("登录窗口测试完成，5秒后自动关闭...")
    
    # 5秒后自动关闭
    root.after(5000, root.quit)
    root.mainloop()
    root.destroy()

def test_main_window_size():
    """测试主窗口大小"""
    print("\n测试主窗口大小...")
    
    root = tk.Tk()
    root.title("发票管理工具 - 主界面")
    root.geometry("900x700")
    root.minsize(800, 600)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="发票管理工具", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 重命名文件格式定义
    format_frame = ttk.LabelFrame(main_frame, text="重命名文件格式定义", padding="10")
    format_frame.pack(fill=tk.X, pady=(0, 10))
    
    format_var = tk.StringVar(value="{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}")
    format_entry = ttk.Entry(format_frame, textvariable=format_var, width=80)
    format_entry.pack(fill=tk.X)
    
    # 重命名文件保存目录
    output_frame = ttk.LabelFrame(main_frame, text="重命名文件保存目录", padding="10")
    output_frame.pack(fill=tk.X, pady=(0, 10))
    
    output_inner_frame = ttk.Frame(output_frame)
    output_inner_frame.pack(fill=tk.X)
    
    output_var = tk.StringVar(value="./output")
    output_entry = ttk.Entry(output_inner_frame, textvariable=output_var)
    output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
    
    browse_button = ttk.Button(output_inner_frame, text="浏览")
    browse_button.pack(side=tk.RIGHT)
    
    # 发票PDF文件或目录
    files_frame = ttk.LabelFrame(main_frame, text="发票PDF文件或目录", padding="10")
    files_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # 文件列表
    list_frame = ttk.Frame(files_frame)
    list_frame.pack(fill=tk.BOTH, expand=True)
    
    files_tree = ttk.Treeview(list_frame, columns=("type", "path"), show="tree headings", height=10)
    files_tree.heading("#0", text="名称")
    files_tree.heading("type", text="类型")
    files_tree.heading("path", text="路径")
    files_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=files_tree.yview)
    files_tree.configure(yscrollcommand=scrollbar.set)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 文件操作按钮
    file_buttons_frame = ttk.Frame(files_frame)
    file_buttons_frame.pack(fill=tk.X, pady=(10, 0))
    
    ttk.Button(file_buttons_frame, text="添加文件").pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(file_buttons_frame, text="添加目录").pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(file_buttons_frame, text="移除选中").pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(file_buttons_frame, text="清空列表").pack(side=tk.LEFT)
    
    # 主操作按钮
    main_buttons_frame = ttk.Frame(main_frame)
    main_buttons_frame.pack(fill=tk.X, pady=(10, 0))
    
    ttk.Button(main_buttons_frame, text="重命名").pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(main_buttons_frame, text="退出", command=root.quit).pack(side=tk.LEFT)
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    print(f"主窗口大小: {width}x{height}")
    print("主窗口测试完成，5秒后自动关闭...")
    
    # 5秒后自动关闭
    root.after(5000, root.quit)
    root.mainloop()
    root.destroy()

def main():
    """主测试函数"""
    print("=" * 50)
    print("界面大小测试")
    print("=" * 50)
    
    try:
        test_login_window_size()
        test_main_window_size()
        
        print("\n" + "=" * 50)
        print("界面测试完成！")
        print("登录窗口已调整为 450x400，最小尺寸 400x350")
        print("主窗口已调整为 900x700，最小尺寸 800x600")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    main()
