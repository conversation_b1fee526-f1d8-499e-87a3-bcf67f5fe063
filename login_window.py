import tkinter as tk
from tkinter import ttk
import threading
from config_manager import ConfigManager
from toast import Toast
import apiUtil

class LoginWindow:
    """登录窗口"""
    
    def __init__(self, on_login_success=None):
        self.on_login_success = on_login_success
        self.config_manager = ConfigManager()
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("发票管理工具 - 登录")
        self.root.geometry("500x580")
        self.root.resizable(True, True)
        self.root.minsize(450, 520)
        
        # 居中显示窗口
        self._center_window()
        
        # 创建Toast组件
        self.toast = Toast(self.root)
        
        # 创建界面
        self._create_widgets()
        
        # 加载配置
        self._load_config()
        
        # 如果设置了自动登录，则自动执行登录
        if self.auto_login_var.get():
            self.root.after(500, self._auto_login)
    
    def _center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 使用Grid布局管理器，更精确控制位置
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # 主框架
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.grid(row=0, column=0, sticky="nsew")

        # 配置主框架的行权重
        for i in range(10):
            main_frame.grid_rowconfigure(i, weight=0)
        main_frame.grid_columnconfigure(0, weight=1)

        row = 0

        # 标题
        title_label = ttk.Label(main_frame, text="发票管理工具", font=("Arial", 18, "bold"))
        title_label.grid(row=row, column=0, pady=(0, 30), sticky="ew")
        row += 1

        # 服务器地址
        ttk.Label(main_frame, text="服务器地址:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
        row += 1
        self.server_var = tk.StringVar()
        server_entry = ttk.Entry(main_frame, textvariable=self.server_var, font=("Arial", 10))
        server_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
        row += 1

        # 端口
        ttk.Label(main_frame, text="端口:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
        row += 1
        self.port_var = tk.StringVar()
        port_entry = ttk.Entry(main_frame, textvariable=self.port_var, font=("Arial", 10))
        port_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
        row += 1

        # 用户名
        ttk.Label(main_frame, text="用户名:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
        row += 1
        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(main_frame, textvariable=self.username_var, font=("Arial", 10))
        username_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
        row += 1

        # 密码
        ttk.Label(main_frame, text="密码:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
        row += 1
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", font=("Arial", 10))
        password_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
        row += 1

        # 自动登录复选框
        self.auto_login_var = tk.BooleanVar()
        auto_login_check = ttk.Checkbutton(main_frame, text="自动登录", variable=self.auto_login_var)
        auto_login_check.grid(row=row, column=0, sticky="w", pady=(0, 20))
        row += 1

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, pady=(25, 0))
        row += 1

        # 登录按钮 - 更大更高的尺寸
        self.login_button = ttk.Button(button_frame, text="登录", command=self._on_login_click,
                                     width=12)
        self.login_button.grid(row=0, column=0, padx=(0, 20), ipadx=20, ipady=18)

        # 退出按钮 - 更大更高的尺寸
        exit_button = ttk.Button(button_frame, text="退出", command=self._on_exit_click,
                               width=12)
        exit_button.grid(row=0, column=1, ipadx=20, ipady=18)

        # 状态栏
        self.status_label = ttk.Label(main_frame, text="请输入登录信息",
                                    font=("Arial", 9), foreground="gray")
        self.status_label.grid(row=row, column=0, pady=(20, 30))

        # 绑定回车键到登录
        self.root.bind('<Return>', lambda e: self._on_login_click())

        # 设置焦点到服务器地址输入框
        server_entry.focus()
    
    def _load_config(self):
        """加载配置"""
        config = self.config_manager.get_login_config()
        self.server_var.set(config["server"])
        self.port_var.set(config["port"])
        self.username_var.set(config["username"])
        self.password_var.set(config["password"])
        self.auto_login_var.set(config["auto_login"])
    
    def _save_config(self):
        """保存配置"""
        self.config_manager.save_login_config(
            self.server_var.get(),
            self.port_var.get(),
            self.username_var.get(),
            self.password_var.get(),
            self.auto_login_var.get()
        )
    
    def _auto_login(self):
        """自动登录"""
        if self.auto_login_var.get():
            self._on_login_click()
    
    def _on_login_click(self):
        """登录按钮点击事件"""
        # 验证输入
        if not self.server_var.get().strip():
            self.toast.show_error("请输入服务器地址")
            self.status_label.config(text="请输入服务器地址", foreground="red")
            return

        if not self.port_var.get().strip():
            self.toast.show_error("请输入端口")
            self.status_label.config(text="请输入端口", foreground="red")
            return

        if not self.username_var.get().strip():
            self.toast.show_error("请输入用户名")
            self.status_label.config(text="请输入用户名", foreground="red")
            return

        if not self.password_var.get().strip():
            self.toast.show_error("请输入密码")
            self.status_label.config(text="请输入密码", foreground="red")
            return

        # 禁用登录按钮，防止重复点击
        self.login_button.config(state=tk.DISABLED, text="登录中...")
        self.status_label.config(text="正在连接服务器...", foreground="blue")

        # 在新线程中执行登录
        threading.Thread(target=self._do_login, daemon=True).start()
    
    def _do_login(self):
        """执行登录操作"""
        try:
            # 创建参数对象（模拟命令行参数）
            class Args:
                def __init__(self, server, port, username, password):
                    self.server = server
                    self.port = port
                    self.username = username
                    self.password = password
                    self.token = None

            args = Args(
                self.server_var.get().strip(),
                self.port_var.get().strip(),
                self.username_var.get().strip(),
                self.password_var.get().strip()
            )

            # 调用登录API，添加超时处理
            token = self._api_login_with_timeout(args)

            # 在主线程中处理结果
            self.root.after(0, lambda: self._handle_login_result(token, args))

        except Exception as e:
            # 在主线程中显示错误
            self.root.after(0, lambda: self._handle_login_error(str(e)))

    def _api_login_with_timeout(self, args):
        """带超时的登录API调用"""
        import requests
        import json

        url = "http://{}:{}/auth/login".format(args.server, args.port)

        payload = json.dumps({
            "password": args.password,
            "username": args.username,
        })

        headers = {
            'Content-Type': 'application/json'
        }

        try:
            # 设置10秒超时
            resp = requests.request("POST", url, headers=headers, data=payload, timeout=10)
            resp.encoding = "utf-8"

            args.token = None
            if resp.status_code == 200:
                data = json.loads(resp.text)
                if 'data' in data and 'token' in data['data']:
                    args.token = data['data']['token']
                else:
                    raise Exception("服务器响应格式错误")
            elif resp.status_code == 401:
                raise Exception("用户名或密码错误")
            elif resp.status_code == 404:
                raise Exception("服务器地址或端口错误")
            else:
                raise Exception(f"服务器错误 (HTTP {resp.status_code})")

            return args.token

        except requests.exceptions.Timeout:
            raise Exception("连接超时，请检查服务器地址和端口")
        except requests.exceptions.ConnectionError:
            raise Exception("无法连接到服务器，请检查服务器地址和端口")
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError:
            raise Exception("服务器响应格式错误")
        except Exception as e:
            if "服务器" in str(e) or "连接" in str(e) or "超时" in str(e):
                raise e
            else:
                raise Exception(f"登录失败: {str(e)}")
    
    def _handle_login_result(self, token, args):
        """处理登录结果"""
        # 恢复登录按钮
        self.login_button.config(state=tk.NORMAL, text="登录")

        if token and token.strip():
            # 登录成功
            args.token = token  # 确保token被设置到args中
            self.toast.show_success("登录成功")
            self.status_label.config(text="登录成功，正在打开主界面...", foreground="green")

            # 保存配置
            self._save_config()

            # 延迟关闭登录窗口并打开主窗口
            self.root.after(2000, lambda: self._open_main_window(args))
        else:
            # 登录失败
            self.toast.show_error("登录失败，请检查服务器地址、端口、用户名和密码")
            self.status_label.config(text="登录失败，请检查输入信息", foreground="red")

    def _handle_login_error(self, error_msg):
        """处理登录错误"""
        # 恢复登录按钮
        self.login_button.config(state=tk.NORMAL, text="登录")

        # 显示错误信息
        self.toast.show_error(f"登录失败: {error_msg}")
        self.status_label.config(text=f"连接错误: {error_msg}", foreground="red")
    
    def _open_main_window(self, args):
        """打开主窗口"""
        if self.on_login_success:
            self.on_login_success(args)
        self.root.destroy()
    
    def _on_exit_click(self):
        """退出按钮点击事件"""
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行登录窗口"""
        self.root.mainloop()
