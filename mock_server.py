#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模拟服务器，用于测试登录功能
"""

import json
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

class MockServerHandler(BaseHTTPRequestHandler):
    """模拟服务器处理器"""
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/auth/login':
            self._handle_login()
        elif self.path == '/auth/version':
            self._handle_version()
        else:
            self._send_error(404, "Not Found")
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/auth/version':
            self._handle_version()
        else:
            self._send_error(404, "Not Found")
    
    def _handle_login(self):
        """处理登录请求"""
        try:
            # 读取请求体
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            # 解析JSON数据
            data = json.loads(post_data.decode('utf-8'))
            username = data.get('username', '')
            password = data.get('password', '')
            
            print(f"登录请求: 用户名={username}, 密码={password}")
            
            # 模拟登录验证
            if username == 'admin' and password == '123456':
                # 登录成功
                response = {
                    "code": 200,
                    "message": "登录成功",
                    "data": {
                        "token": "mock_token_12345",
                        "username": username,
                        "expires": int(time.time()) + 3600  # 1小时后过期
                    }
                }
                self._send_json_response(200, response)
            else:
                # 登录失败
                response = {
                    "code": 401,
                    "message": "用户名或密码错误",
                    "data": None
                }
                self._send_json_response(401, response)
                
        except json.JSONDecodeError:
            self._send_error(400, "Invalid JSON")
        except Exception as e:
            print(f"登录处理错误: {e}")
            self._send_error(500, "Internal Server Error")
    
    def _handle_version(self):
        """处理版本请求"""
        response = {
            "code": 200,
            "message": "成功",
            "data": "Mock Server v1.0.0"
        }
        self._send_json_response(200, response)
    
    def _send_json_response(self, status_code, data):
        """发送JSON响应"""
        response_data = json.dumps(data, ensure_ascii=False)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response_data.encode('utf-8'))))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        self.wfile.write(response_data.encode('utf-8'))
    
    def _send_error(self, status_code, message):
        """发送错误响应"""
        response = {
            "code": status_code,
            "message": message,
            "data": None
        }
        self._send_json_response(status_code, response)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.address_string()}] {format % args}")

class MockServer:
    """模拟服务器"""
    
    def __init__(self, host='localhost', port=8881):
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
    
    def start(self):
        """启动服务器"""
        try:
            self.server = HTTPServer((self.host, self.port), MockServerHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            print(f"模拟服务器启动成功: http://{self.host}:{self.port}")
            print("支持的端点:")
            print("  POST /auth/login - 登录 (用户名: admin, 密码: 123456)")
            print("  GET  /auth/version - 获取版本信息")
            return True
        except Exception as e:
            print(f"服务器启动失败: {e}")
            return False
    
    def stop(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("模拟服务器已停止")
    
    def is_running(self):
        """检查服务器是否运行"""
        return self.server is not None and self.server_thread is not None and self.server_thread.is_alive()

def main():
    """主函数"""
    print("=" * 50)
    print("发票管理工具 - 模拟服务器")
    print("=" * 50)
    
    # 创建并启动服务器
    server = MockServer()
    
    if server.start():
        try:
            print("\n服务器运行中...")
            print("按 Ctrl+C 停止服务器")
            
            # 保持服务器运行
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n收到停止信号...")
        finally:
            server.stop()
    else:
        print("服务器启动失败")

if __name__ == "__main__":
    main()
