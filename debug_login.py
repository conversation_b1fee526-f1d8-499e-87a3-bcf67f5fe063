#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试登录功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_login():
    """调试登录功能"""
    print("=" * 50)
    print("调试登录功能")
    print("=" * 50)
    
    try:
        from login_window import LoginWindow
        
        def on_login_success(args):
            print(f"\n🎉 登录成功回调被调用！")
            print(f"服务器: {args.server}:{args.port}")
            print(f"用户: {args.username}")
            print(f"Token: {getattr(args, 'token', 'No token')}")
        
        print("创建登录窗口...")
        login_window = LoginWindow(on_login_success=on_login_success)
        
        print("登录窗口已创建")
        print("请在界面中点击登录按钮")
        print("观察控制台的调试输出")
        print("注意登录过程中的状态变化")
        
        # 运行登录窗口
        login_window.run()
        
        print("登录窗口已关闭")
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_login()
