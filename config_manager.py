import json
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器，用于保存和加载用户设置"""
    
    def __init__(self, config_file: str = "invoice_config.json"):
        self.config_file = config_file
        self.default_config = {
            # 登录相关配置
            "server": "localhost",
            "port": "8881", 
            "username": "admin",
            "password": "123456",
            "auto_login": False,
            
            # 主界面配置
            "format": "{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}",
            "output": "./output",
            "invoice_files": [],  # PDF文件或目录列表
            "last_browse_dir": "."  # 上次浏览的目录
        }
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件，如果不存在则返回默认配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有必要的键都存在
                    merged_config = self.default_config.copy()
                    merged_config.update(config)
                    return merged_config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get_login_config(self) -> Dict[str, Any]:
        """获取登录相关配置"""
        config = self.load_config()
        return {
            "server": config.get("server", self.default_config["server"]),
            "port": config.get("port", self.default_config["port"]),
            "username": config.get("username", self.default_config["username"]),
            "password": config.get("password", self.default_config["password"]),
            "auto_login": config.get("auto_login", self.default_config["auto_login"])
        }
    
    def save_login_config(self, server: str, port: str, username: str, 
                         password: str, auto_login: bool) -> bool:
        """保存登录配置"""
        config = self.load_config()
        config.update({
            "server": server,
            "port": port,
            "username": username,
            "password": password,
            "auto_login": auto_login
        })
        return self.save_config(config)
    
    def get_main_config(self) -> Dict[str, Any]:
        """获取主界面相关配置"""
        config = self.load_config()
        return {
            "format": config.get("format", self.default_config["format"]),
            "output": config.get("output", self.default_config["output"]),
            "invoice_files": config.get("invoice_files", self.default_config["invoice_files"]),
            "last_browse_dir": config.get("last_browse_dir", self.default_config["last_browse_dir"])
        }
    
    def save_main_config(self, format_str: str, output: str,
                        invoice_files: list, last_browse_dir: str = None) -> bool:
        """保存主界面配置"""
        config = self.load_config()
        update_data = {
            "format": format_str,
            "output": output,
            "invoice_files": invoice_files
        }
        if last_browse_dir is not None:
            update_data["last_browse_dir"] = last_browse_dir
        config.update(update_data)
        return self.save_config(config)
    
    def update_config(self, **kwargs) -> bool:
        """更新配置中的指定项"""
        config = self.load_config()
        config.update(kwargs)
        return self.save_config(config)
