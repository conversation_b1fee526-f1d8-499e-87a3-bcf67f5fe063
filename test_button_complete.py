#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试按钮是否完整显示
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_button_complete():
    """测试按钮完整显示"""
    print("测试按钮完整显示...")
    
    root = tk.Tk()
    root.title("发票管理工具 - 登录")
    root.geometry("500x520")  # 增加高度
    root.resizable(True, True)
    root.minsize(450, 480)
    
    # 使用Grid布局管理器
    root.grid_rowconfigure(0, weight=1)
    root.grid_columnconfigure(0, weight=1)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="30")
    main_frame.grid(row=0, column=0, sticky="nsew")
    
    # 配置主框架的行权重
    for i in range(10):
        main_frame.grid_rowconfigure(i, weight=0)
    main_frame.grid_columnconfigure(0, weight=1)
    
    row = 0
    
    # 标题
    title_label = ttk.Label(main_frame, text="发票管理工具", font=("Arial", 18, "bold"))
    title_label.grid(row=row, column=0, pady=(0, 30), sticky="ew")
    row += 1
    
    # 服务器地址
    ttk.Label(main_frame, text="服务器地址:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
    row += 1
    server_var = tk.StringVar(value="localhost")
    server_entry = ttk.Entry(main_frame, textvariable=server_var, font=("Arial", 10))
    server_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
    row += 1
    
    # 端口
    ttk.Label(main_frame, text="端口:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
    row += 1
    port_var = tk.StringVar(value="8881")
    port_entry = ttk.Entry(main_frame, textvariable=port_var, font=("Arial", 10))
    port_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
    row += 1
    
    # 用户名
    ttk.Label(main_frame, text="用户名:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
    row += 1
    username_var = tk.StringVar(value="admin")
    username_entry = ttk.Entry(main_frame, textvariable=username_var, font=("Arial", 10))
    username_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
    row += 1
    
    # 密码
    ttk.Label(main_frame, text="密码:", font=("Arial", 10)).grid(row=row, column=0, sticky="w", pady=(0, 5))
    row += 1
    password_var = tk.StringVar(value="123456")
    password_entry = ttk.Entry(main_frame, textvariable=password_var, show="*", font=("Arial", 10))
    password_entry.grid(row=row, column=0, sticky="ew", pady=(0, 15))
    row += 1
    
    # 自动登录复选框
    auto_login_var = tk.BooleanVar()
    auto_login_check = ttk.Checkbutton(main_frame, text="自动登录", variable=auto_login_var)
    auto_login_check.grid(row=row, column=0, sticky="w", pady=(0, 20))
    row += 1
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.grid(row=row, column=0, pady=(25, 0))
    row += 1
    
    # 登录按钮 - 更大更高的尺寸
    login_button = ttk.Button(button_frame, text="登录", width=12)
    login_button.grid(row=0, column=0, padx=(0, 20), ipadx=20, ipady=18)
    
    # 退出按钮 - 更大更高的尺寸
    exit_button = ttk.Button(button_frame, text="退出", command=root.quit, width=12)
    exit_button.grid(row=0, column=1, ipadx=20, ipady=18)
    
    # 状态栏 - 增加底部间距
    status_label = ttk.Label(main_frame, text="请输入登录信息", 
                           font=("Arial", 9), foreground="gray")
    status_label.grid(row=row, column=0, pady=(20, 30))
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    print(f"窗口大小: {width}x{height}")
    print("检查按钮是否完整显示...")
    
    # 显示详细的布局信息
    def show_complete_layout_info():
        print("\n详细布局信息:")
        print(f"窗口尺寸: {root.winfo_width()}x{root.winfo_height()}")
        print(f"主框架尺寸: {main_frame.winfo_width()}x{main_frame.winfo_height()}")
        print(f"主框架位置: {main_frame.winfo_x()}, {main_frame.winfo_y()}")
        
        print(f"\n按钮信息:")
        print(f"登录按钮尺寸: {login_button.winfo_width()}x{login_button.winfo_height()}")
        print(f"登录按钮位置: {login_button.winfo_rootx()}, {login_button.winfo_rooty()}")
        print(f"退出按钮尺寸: {exit_button.winfo_width()}x{exit_button.winfo_height()}")
        print(f"退出按钮位置: {exit_button.winfo_rootx()}, {exit_button.winfo_rooty()}")
        
        print(f"\n按钮框架信息:")
        print(f"按钮框架尺寸: {button_frame.winfo_width()}x{button_frame.winfo_height()}")
        print(f"按钮框架位置: {button_frame.winfo_x()}, {button_frame.winfo_y()}")
        
        print(f"\n状态栏信息:")
        print(f"状态栏尺寸: {status_label.winfo_width()}x{status_label.winfo_height()}")
        print(f"状态栏位置: {status_label.winfo_x()}, {status_label.winfo_y()}")
        
        # 检查按钮是否在窗口范围内
        button_bottom = button_frame.winfo_y() + button_frame.winfo_height()
        status_bottom = status_label.winfo_y() + status_label.winfo_height()
        window_height = root.winfo_height()
        
        print(f"\n可见性检查:")
        print(f"按钮框架底部位置: {button_bottom}")
        print(f"状态栏底部位置: {status_bottom}")
        print(f"窗口高度: {window_height}")
        print(f"按钮完全可见: {button_bottom < window_height}")
        print(f"状态栏完全可见: {status_bottom < window_height}")
        
        # 检查是否有足够的底部间距
        bottom_margin = window_height - status_bottom
        print(f"底部间距: {bottom_margin}px")
        print(f"底部间距充足 (>20px): {bottom_margin > 20}")
    
    root.after(1000, show_complete_layout_info)
    print("测试窗口将在10秒后自动关闭...")
    root.after(10000, root.quit)
    
    root.mainloop()
    root.destroy()

def main():
    """主测试函数"""
    print("=" * 60)
    print("按钮完整显示测试")
    print("=" * 60)
    
    try:
        test_button_complete()
        print("\n测试完成！")
        print("窗口调整:")
        print("1. 窗口高度增加到 520px")
        print("2. 最小高度设置为 480px")
        print("3. 底部间距增加到 30px")
        print("4. 按钮高度设置为 ipady=18")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
