1. 使用python开发一个桌面应用（invoiceApp.py），实现与命令行应用（invoice.py）相同的功能，并提供可视化操作界面
2. 启动invoiceApp.py后，首先进入登录界面
2.1. 登录界面有：服务器地址、端口、用户名、密码、是否自动登录，这些字段；
2.2. 这些字段都有默认值，用户如修改设置后，应用需记录用户的设置值，下次启动时使用上次的设置值；
2.3. 登录界面提供：登录、退出，两个按钮；
2.4. 点击登录按钮，进行登录操作；登录操作的反馈：登录成功 或 登录失败及失败信息，以“Toast 轻提示”的方式显示给用户；
2.5. 登录成功后，自动进入主界面；登录失败，则停留在登录界面
2.6. 点击退出按钮，退出应用
3. 主界面包括以下设置字段：重命名文件格式定义、重命名文件保存目录、发票PDF文件或目录
3.1. 重命名文件格式定义：文本输入框，默认值为{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}，用户可修改，修改后应用需记录用户的设置值，下次启动时使用上次的设置值；
3.2. 重命名文件保存目录：路径选择输入框，默认值为./output，用户可修改，修改后应用需记录用户的设置值，下次启动时使用上次的设置值；
3.3. 发票PDF文件或目录：列表输入框，默认值为空，用户可添加多个文件或目录，支持文件或目录的拖拉投放输入，修改后应用需记录用户的设置值，下次启动时使用上次的设置值；
4. 主界面提供：重命名、退出，两个按钮；
4.1. 点击重命名按钮，进行重命名操作；重命名操作的反馈：重命名成功 或 重命名失败及失败信息，以“Toast 轻提示”的方式显示给用户；
4.2. 点击退出按钮，退出应用