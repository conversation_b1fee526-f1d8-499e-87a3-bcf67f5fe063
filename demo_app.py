#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
发票管理工具桌面应用演示脚本
在没有GUI环境的情况下演示应用功能
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager

def demo_config_manager():
    """演示配置管理功能"""
    print("=" * 50)
    print("配置管理功能演示")
    print("=" * 50)
    
    # 创建配置管理器
    config_manager = ConfigManager("demo_config.json")
    
    # 显示默认配置
    print("1. 默认配置：")
    default_config = config_manager.load_config()
    for key, value in default_config.items():
        print(f"   {key}: {value}")
    
    # 保存登录配置
    print("\n2. 保存登录配置...")
    config_manager.save_login_config(
        server="demo.server.com",
        port="9999",
        username="demo_user",
        password="demo_pass",
        auto_login=True
    )
    print("   ✓ 登录配置已保存")
    
    # 保存主界面配置
    print("\n3. 保存主界面配置...")
    config_manager.save_main_config(
        format_str="{buyerId}_{invoiceNo}_{amountIncludingTax}",
        output="./demo_output",
        invoice_files=["file1.pdf", "file2.pdf", "/path/to/directory"]
    )
    print("   ✓ 主界面配置已保存")
    
    # 读取配置
    print("\n4. 读取保存的配置：")
    login_config = config_manager.get_login_config()
    main_config = config_manager.get_main_config()
    
    print("   登录配置：")
    for key, value in login_config.items():
        print(f"     {key}: {value}")
    
    print("   主界面配置：")
    for key, value in main_config.items():
        print(f"     {key}: {value}")
    
    # 显示配置文件内容
    print("\n5. 配置文件内容：")
    if os.path.exists("demo_config.json"):
        with open("demo_config.json", 'r', encoding='utf-8') as f:
            config_content = json.load(f)
        print(json.dumps(config_content, ensure_ascii=False, indent=2))
    
    # 清理演示文件
    if os.path.exists("demo_config.json"):
        os.remove("demo_config.json")
    print("\n   ✓ 演示文件已清理")

def demo_application_flow():
    """演示应用程序流程"""
    print("\n" + "=" * 50)
    print("应用程序流程演示")
    print("=" * 50)
    
    print("1. 应用启动流程：")
    print("   ├── 检查依赖库")
    print("   ├── 创建登录窗口")
    print("   ├── 加载保存的登录配置")
    print("   ├── 用户输入登录信息")
    print("   ├── 连接服务器验证")
    print("   └── 登录成功后打开主窗口")
    
    print("\n2. 登录界面功能：")
    print("   ├── 服务器地址输入框 (默认: localhost)")
    print("   ├── 端口输入框 (默认: 8881)")
    print("   ├── 用户名输入框 (默认: admin)")
    print("   ├── 密码输入框 (默认: 123456)")
    print("   ├── 自动登录复选框")
    print("   ├── 登录按钮")
    print("   └── 退出按钮")
    
    print("\n3. 主界面功能：")
    print("   ├── 重命名格式定义输入框")
    print("   ├── 输出目录选择")
    print("   ├── PDF文件列表管理")
    print("   │   ├── 添加文件按钮")
    print("   │   ├── 添加目录按钮")
    print("   │   ├── 移除选中按钮")
    print("   │   ├── 清空列表按钮")
    print("   │   └── 拖拽支持 (可选)")
    print("   ├── 重命名按钮")
    print("   └── 退出按钮")
    
    print("\n4. 重命名处理流程：")
    print("   ├── 验证输入参数")
    print("   ├── 创建输出目录")
    print("   ├── 遍历PDF文件")
    print("   ├── 上传文件到服务器")
    print("   ├── 获取发票信息")
    print("   ├── 生成新文件名")
    print("   ├── 复制文件到输出目录")
    print("   └── 显示处理结果")

def demo_supported_fields():
    """演示支持的字段"""
    print("\n" + "=" * 50)
    print("重命名格式支持的字段")
    print("=" * 50)
    
    fields = {
        "invoicingDate": "开票日期",
        "invoiceNo": "发票号码", 
        "amountIncludingTax": "含税金额",
        "tax": "税费",
        "buyerName": "购买方名称",
        "buyerId": "购买方税号",
        "sellerName": "销售方名称",
        "sellerId": "销售方税号"
    }
    
    print("可用字段：")
    for field, description in fields.items():
        print(f"   {{{field}}} - {description}")
    
    print("\n格式示例：")
    examples = [
        "{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}",
        "{invoiceNo}_{buyerName}_{amountIncludingTax}",
        "{invoicingDate}_{buyerId}_{invoiceNo}",
        "{sellerName}_{buyerName}_{invoiceNo}_{amountIncludingTax}"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"   {i}. {example}")

def demo_file_structure():
    """演示文件结构"""
    print("\n" + "=" * 50)
    print("应用文件结构")
    print("=" * 50)
    
    structure = {
        "invoiceApp.py": "主应用程序入口",
        "config_manager.py": "配置管理模块",
        "login_window.py": "登录界面实现",
        "main_window.py": "主界面实现",
        "toast.py": "Toast提示组件",
        "drag_drop.py": "拖拽功能支持",
        "run_invoice_app.py": "启动脚本",
        "run_invoice_app.bat": "Windows批处理启动脚本",
        "test_app.py": "测试脚本",
        "demo_app.py": "演示脚本",
        "README_invoiceApp.md": "使用说明文档",
        "invoice_config.json": "配置文件（自动生成）"
    }
    
    print("文件列表：")
    for filename, description in structure.items():
        exists = "✓" if os.path.exists(filename) else "✗"
        print(f"   {exists} {filename:<25} - {description}")

def main():
    """主演示函数"""
    print("发票管理工具桌面应用 - 功能演示")
    print("版本：1.0.0")
    print("开发基于原有命令行工具，提供图形界面支持")
    
    # 运行各个演示
    demo_config_manager()
    demo_application_flow()
    demo_supported_fields()
    demo_file_structure()
    
    print("\n" + "=" * 50)
    print("演示完成")
    print("=" * 50)
    print("要运行实际应用，请在支持GUI的环境中执行：")
    print("  python invoiceApp.py")
    print("或使用启动脚本：")
    print("  python run_invoice_app.py")

if __name__ == "__main__":
    main()
