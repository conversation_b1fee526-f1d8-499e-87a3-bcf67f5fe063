import tkinter as tk
from tkinter import ttk
import threading
import time

class Toast:
    """Toast轻提示组件"""
    
    def __init__(self, parent):
        self.parent = parent
        self.toast_window = None
        
    def show(self, message: str, duration: int = 3000, toast_type: str = "info"):
        """
        显示Toast提示
        
        Args:
            message: 提示消息
            duration: 显示时长（毫秒）
            toast_type: 提示类型 ("success", "error", "warning", "info")
        """
        # 如果已有Toast窗口，先关闭
        if self.toast_window:
            try:
                self.toast_window.destroy()
            except:
                pass
        
        # 创建Toast窗口
        self.toast_window = tk.Toplevel(self.parent)
        self.toast_window.withdraw()  # 先隐藏窗口
        
        # 设置窗口属性
        self.toast_window.overrideredirect(True)  # 无边框
        self.toast_window.attributes('-topmost', True)  # 置顶
        
        # 根据类型设置颜色
        colors = {
            "success": {"bg": "#4CAF50", "fg": "white"},
            "error": {"bg": "#F44336", "fg": "white"},
            "warning": {"bg": "#FF9800", "fg": "white"},
            "info": {"bg": "#2196F3", "fg": "white"}
        }
        
        color_config = colors.get(toast_type, colors["info"])
        
        # 创建标签
        label = tk.Label(
            self.toast_window,
            text=message,
            bg=color_config["bg"],
            fg=color_config["fg"],
            font=("Arial", 10),
            padx=20,
            pady=10
        )
        label.pack()
        
        # 计算窗口位置（居中显示在父窗口上方）
        self.toast_window.update_idletasks()
        
        # 获取父窗口位置和大小
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        
        # 获取Toast窗口大小
        toast_width = self.toast_window.winfo_reqwidth()
        toast_height = self.toast_window.winfo_reqheight()
        
        # 计算居中位置
        x = parent_x + (parent_width - toast_width) // 2
        y = parent_y + 50  # 距离父窗口顶部50像素
        
        # 设置窗口位置
        self.toast_window.geometry(f"{toast_width}x{toast_height}+{x}+{y}")
        
        # 显示窗口
        self.toast_window.deiconify()
        
        # 设置自动关闭
        self.parent.after(duration, self._close_toast)
        
        # 添加淡入效果（可选）
        self._fade_in()
    
    def _fade_in(self):
        """淡入效果"""
        try:
            self.toast_window.attributes('-alpha', 0.0)
            for i in range(1, 11):
                if self.toast_window:
                    self.toast_window.attributes('-alpha', i / 10.0)
                    self.toast_window.update()
                    time.sleep(0.02)
        except:
            pass
    
    def _close_toast(self):
        """关闭Toast窗口"""
        if self.toast_window:
            try:
                # 淡出效果
                for i in range(10, 0, -1):
                    if self.toast_window:
                        self.toast_window.attributes('-alpha', i / 10.0)
                        self.toast_window.update()
                        time.sleep(0.02)
                
                self.toast_window.destroy()
                self.toast_window = None
            except:
                pass
    
    def show_success(self, message: str, duration: int = 3000):
        """显示成功提示"""
        self.show(message, duration, "success")
    
    def show_error(self, message: str, duration: int = 3000):
        """显示错误提示"""
        self.show(message, duration, "error")
    
    def show_warning(self, message: str, duration: int = 3000):
        """显示警告提示"""
        self.show(message, duration, "warning")
    
    def show_info(self, message: str, duration: int = 3000):
        """显示信息提示"""
        self.show(message, duration, "info")
