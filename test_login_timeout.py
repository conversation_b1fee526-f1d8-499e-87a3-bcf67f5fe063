#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试登录超时处理
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_login_timeout():
    """测试登录超时处理"""
    print("=" * 60)
    print("登录超时处理测试")
    print("=" * 60)
    
    try:
        from login_window import LoginWindow
        
        def on_login_success(args):
            print(f"\n登录成功！")
            print(f"服务器: {args.server}:{args.port}")
            print(f"用户: {args.username}")
        
        # 创建登录窗口
        print("创建登录窗口...")
        login_window = LoginWindow(on_login_success=on_login_success)
        
        print("登录窗口已创建")
        print("\n测试场景:")
        print("1. 默认设置 (localhost:8881) - 可能超时")
        print("2. 错误端口 (localhost:9999) - 连接失败")
        print("3. 错误地址 (nonexistent.server:8881) - DNS解析失败")
        print("4. 正确设置但服务器未运行 - 连接被拒绝")
        
        print("\n改进功能:")
        print("✓ 10秒连接超时")
        print("✓ 详细错误信息")
        print("✓ 自动恢复登录按钮")
        print("✓ 状态栏显示错误信息")
        
        print("\n请在界面中测试不同的登录场景")
        print("观察错误处理和超时行为")
        
        # 运行登录窗口
        login_window.run()
        
        print("\n登录测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_timeout_function():
    """单独测试超时功能"""
    print("\n" + "=" * 40)
    print("单独测试超时功能")
    print("=" * 40)
    
    try:
        from login_window import LoginWindow
        
        # 创建临时登录窗口实例来测试超时函数
        temp_window = LoginWindow()
        
        # 创建测试参数
        class TestArgs:
            def __init__(self, server, port, username, password):
                self.server = server
                self.port = port
                self.username = username
                self.password = password
                self.token = None
        
        # 测试不同的错误场景
        test_cases = [
            ("localhost", "9999", "admin", "123456", "连接被拒绝"),
            ("nonexistent.server.com", "8881", "admin", "123456", "DNS解析失败"),
            ("192.168.1.999", "8881", "admin", "123456", "连接超时"),
        ]
        
        for server, port, username, password, expected_error in test_cases:
            print(f"\n测试: {server}:{port}")
            args = TestArgs(server, port, username, password)
            
            start_time = time.time()
            try:
                token = temp_window._api_login_with_timeout(args)
                print(f"意外成功: {token}")
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"错误 ({duration:.1f}s): {e}")
                print(f"预期错误类型: {expected_error}")
        
        # 清理
        temp_window.root.destroy()
        
    except Exception as e:
        print(f"超时测试失败: {e}")

def main():
    """主测试函数"""
    print("登录超时和错误处理测试")
    
    # 首先测试超时功能
    test_timeout_function()
    
    # 然后测试完整的登录界面
    test_login_timeout()

if __name__ == "__main__":
    main()
