#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试API调用
"""

import sys
import os
import requests
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_direct():
    """直接测试API调用"""
    print("直接测试登录API")
    print("=" * 30)
    
    url = "http://localhost:8881/auth/login"
    
    payload = json.dumps({
        "password": "123456",
        "username": "admin",
    })
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        print("发送登录请求...")
        resp = requests.request("POST", url, headers=headers, data=payload, timeout=10)
        resp.encoding = "utf-8"
        
        print(f"状态码: {resp.status_code}")
        print(f"响应内容: {resp.text}")
        
        if resp.status_code == 200:
            data = json.loads(resp.text)
            print(f"解析后的数据: {data}")
            
            if 'data' in data and 'token' in data['data']:
                token = data['data']['token']
                print(f"✓ 获取到token: {token[:50]}...")
                return token
            else:
                print("✗ 响应中没有token")
                return None
        else:
            print(f"✗ 登录失败，状态码: {resp.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

def test_login_window_api():
    """测试登录窗口的API函数"""
    print("\n测试登录窗口的API函数")
    print("=" * 30)
    
    try:
        from login_window import LoginWindow
        
        # 创建临时登录窗口实例
        temp_window = LoginWindow()
        
        class TestArgs:
            def __init__(self, server, port, username, password):
                self.server = server
                self.port = port
                self.username = username
                self.password = password
                self.token = None
        
        args = TestArgs("localhost", "8881", "admin", "123456")
        
        print("调用登录窗口的API函数...")
        token = temp_window._api_login_with_timeout(args)
        
        print(f"✓ 登录成功，token: {token[:50]}..." if token else "✗ 登录失败")
        
        # 清理
        temp_window.root.destroy()
        
        return token
        
    except Exception as e:
        print(f"✗ 登录窗口API测试失败: {e}")
        return None

def main():
    """主函数"""
    print("API调用测试")
    print("=" * 50)
    
    # 测试直接API调用
    token1 = test_api_direct()
    
    # 测试登录窗口的API函数
    token2 = test_login_window_api()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"直接API调用: {'成功' if token1 else '失败'}")
    print(f"登录窗口API: {'成功' if token2 else '失败'}")
    
    if token1 and token2:
        print("✓ 所有API调用都成功")
    else:
        print("✗ 存在API调用问题")

if __name__ == "__main__":
    main()
