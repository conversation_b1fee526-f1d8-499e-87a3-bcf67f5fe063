#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速登录功能测试
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试登录功能"""
    print("快速登录功能测试")
    print("=" * 40)
    
    try:
        from login_window import LoginWindow
        
        # 创建临时登录窗口实例
        temp_window = LoginWindow()
        
        class TestArgs:
            def __init__(self, server, port, username, password):
                self.server = server
                self.port = port
                self.username = username
                self.password = password
                self.token = None
        
        # 测试本地服务器
        args = TestArgs("localhost", "8881", "admin", "123456")
        
        print("测试登录...")
        start_time = time.time()
        
        try:
            token = temp_window._api_login_with_timeout(args)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✓ 登录成功 ({duration:.1f}s)")
            print(f"Token: {token[:30]}..." if token else "No token")
            
            # 测试版本信息
            try:
                import apiUtil
                version = apiUtil.getVersion(args)
                print(f"✓ 服务器版本: {version}")
            except:
                pass
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"✗ 登录失败 ({duration:.1f}s): {e}")
        
        # 清理
        temp_window.root.destroy()
        
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    quick_test()
