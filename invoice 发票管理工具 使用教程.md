# invoice 发票管理工具 使用教程

## 软件的安装
invoice是一款命令行工具软件，需在cmd窗口内，以输入命令的方式运行。invoice软件本身不需安装，只要能以指令方式运行即可。但为了Windows系统能方便的找到invoice.exe程序文件，建议将invoice.exe程序文件复制到Windows系统的system32目录中，即可完成invoice软件的安装。

## 软件的执行
* ```win```+```R```，在输入框输入：cmd```回车```，打开cmd窗口。
* 在cmd窗口，输入```invoice -h``````回车```，如显示以下信息，则invoice软件已正确安装。

 > usage: invoice.exe [option]
> 
> 发票管理工具
> 
> optional arguments:
> 
>   -h, --help            show this help message and exit
> 
>   -v, --version         显示版本
> 
>   -s SERVER, --server SERVER rayoeServer地址（域名或ip） (default: localhost)
> 
>   -p PORT, --port PORT  rayoeServer端口 (default: 8881)
> 
>   -u USERNAME, --username USERNAME 用户名 (default: admin)
> 
>   -w PASSWORD, --password PASSWORD 密码 (default: 123456)
>
>   -f FORMAT, --format FORMAT 重命名文件格式定义。字段名以{}包裹，支持字段名包括：invoicingDate（开票日期），invoiceNo（发票号码），amountIncludingTax（含税金额），tax（税费） (default:{invoicingDate}_{invoiceNo}_{amountIncludingTax})
> 
>   -o OUTPUT, --output OUTPUT 重命名文件保存目录 (default: ./output)
> 
>   -i INVOICE [INVOICE ...], --invoice INVOICE [INVOICE ...] 发票PDF文件或目录，支持通配符及多文件或目录 (default: ['*.pdf'])

## 命令行参数
* \-h ： 显示软件使用的帮助信息，包括：软件命令行格式、各命令行参数格式及功能说明

* \-v ： 显示软件版本号

* \-s ： rayoeServer地址，可以是服务器域名或服务器ip地址。

* \-p ： rayoeServer端口

* \-u ： 用户名

* \-w ： 密码

* \-f ： 重命名文件格式定义

* \-o ： 重命名文件保存目录

* \-i ： 发票PDF文件或目录，支持通配符及多文件或目录（多文件或目录以空格分割；如文件名或目录名包含空格，则使用引号包裹文件名或目录名；文件名或目录名都支持通配符：```*```、```?```） 