#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终的登录窗口
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_final_login():
    """测试最终登录窗口"""
    print("=" * 60)
    print("最终登录窗口测试")
    print("=" * 60)
    print("窗口规格:")
    print("- 尺寸: 500x580")
    print("- 最小尺寸: 450x520")
    print("- 按钮: 更大更高 (width=12, ipadx=20, ipady=18)")
    print("- 布局: Grid布局，水平居中")
    print("- 间距: 充足的顶部和底部间距")
    print("=" * 60)
    
    try:
        from login_window import LoginWindow
        
        def on_login_success(args):
            print(f"\n模拟登录成功！")
            print(f"服务器: {args.server}:{args.port}")
            print(f"用户: {args.username}")
            print("实际应用中这里会打开主窗口")
        
        # 创建登录窗口
        print("创建登录窗口...")
        login_window = LoginWindow(on_login_success=on_login_success)
        
        print("登录窗口已创建并显示")
        print("请检查:")
        print("1. 所有字段是否完整显示")
        print("2. 按钮是否足够大且居中")
        print("3. 按钮是否完全可见")
        print("4. 底部是否有足够间距")
        print("\n可以尝试登录测试（默认值应该已填入）")
        print("关闭窗口或点击退出按钮结束测试")
        
        # 运行登录窗口
        login_window.run()
        
        print("\n登录窗口测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_login()
