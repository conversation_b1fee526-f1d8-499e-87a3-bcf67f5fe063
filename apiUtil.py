# import pysnooper

import requests
import json

# @pysnooper.snoop()
def apiLogin(args):
  url = "http://{}:{}/auth/login".format(args.server, args.port)

  payload = json.dumps({
    "password": args.password,
    "username": args.username,
  })

  headers = {
    'Content-Type': 'application/json'
  }

  resp = requests.request("POST", url, headers=headers, data=payload)
  resp.encoding="utf-8"
  # print("apiLogin:{}".format(response.text))
  # print(resp.status_code)

  args.token=None
  if (resp.status_code==200):
    data = json.loads(resp.text)
    # print(data['data']['token'])
    args.token=data['data']['token']

  return args.token


# @pysnooper.snoop()
def graphql(args, query):
  url = "http://{}:{}/graphql".format(args.server, args.port)

  payload = json.dumps({
    "query": query,
  })

  headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer '+args.token,
  }

  resp = requests.request("POST", url, headers=headers, data=payload)
  resp.encoding="utf-8"
  # print("graphql:{}".format(resp.text))
  # print(resp.status_code)

  if ((resp.status_code==200) and (json.loads(resp.text).get('error')==None)):
    return json.loads(resp.text)

  print("graphql error:{}".format(resp.text))
  return None


# @pysnooper.snoop()
def graphqlUpload(args, payload, files):
  url = "http://{}:{}/graphql".format(args.server, args.port)

  # payload = json.dumps({
  #   "query": query,
  # })

  headers = {
    # 'Content-Type': 'application/json',
    'Authorization': 'Bearer '+args.token,
  }

  resp = requests.request("POST", url, headers=headers, data=payload, files=files)
  resp.encoding="utf-8"
  # print("graphql:{}".format(resp.text))
  # print(resp.status_code)

  if ((resp.status_code==200) and (json.loads(resp.text).get('error')==None)):
    return json.loads(resp.text)

  print("graphqlUpload error:{}".format(resp.text))
  return None


# @pysnooper.snoop()
def getVersion(args):
  url = "http://{}:{}/auth/version".format(args.server, args.port)

  resp = requests.request("GET", url)
  resp.encoding="utf-8"
  # print("getVersion:{}".format(resp.text))
  # print(resp.status_code)

  if ((resp.status_code==200) and (json.loads(resp.text).get('error')==None)):
    return json.loads(resp.text).get('data')

  print("getVersion error:{}".format(resp.text))
  return None