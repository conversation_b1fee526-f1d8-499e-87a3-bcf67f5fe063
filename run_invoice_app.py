#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
发票管理工具桌面应用启动脚本
提供环境检查和友好的启动体验
"""

import sys
import os
import subprocess

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("错误：需要Python 3.6或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    return True

def check_tkinter():
    """检查tkinter支持"""
    try:
        import tkinter
        return True, "tkinter支持正常"
    except ImportError:
        return False, "tkinter不可用，请安装Python的tkinter支持"

def check_dependencies():
    """检查依赖库"""
    required = ['requests']
    optional = {'tkinterdnd2': '拖拽功能'}
    
    missing_required = []
    available_optional = []
    missing_optional = []
    
    # 检查必需依赖
    for module in required:
        try:
            __import__(module)
        except ImportError:
            missing_required.append(module)
    
    # 检查可选依赖
    for module, description in optional.items():
        try:
            __import__(module)
            available_optional.append((module, description))
        except ImportError:
            missing_optional.append((module, description))
    
    return missing_required, available_optional, missing_optional

def install_dependencies(packages):
    """安装依赖包"""
    if not packages:
        return True
    
    print(f"正在安装依赖包：{', '.join(packages)}")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("发票管理工具桌面应用启动器")
    print("=" * 60)
    
    # 检查Python版本
    print("检查Python版本...")
    if not check_python_version():
        input("按回车键退出...")
        sys.exit(1)
    print(f"✓ Python版本：{sys.version.split()[0]}")
    
    # 检查tkinter
    print("\n检查GUI支持...")
    tkinter_ok, tkinter_msg = check_tkinter()
    if tkinter_ok:
        print(f"✓ {tkinter_msg}")
    else:
        print(f"✗ {tkinter_msg}")
        print("请安装Python的tkinter支持包")
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查依赖
    print("\n检查依赖库...")
    missing_required, available_optional, missing_optional = check_dependencies()
    
    # 处理必需依赖
    if missing_required:
        print(f"✗ 缺少必需依赖：{', '.join(missing_required)}")
        response = input("是否自动安装？(y/n): ").lower().strip()
        if response == 'y':
            if install_dependencies(missing_required):
                print("✓ 依赖安装成功")
            else:
                print("✗ 依赖安装失败")
                input("按回车键退出...")
                sys.exit(1)
        else:
            print("请手动安装依赖：pip install " + " ".join(missing_required))
            input("按回车键退出...")
            sys.exit(1)
    else:
        print("✓ 所有必需依赖已满足")
    
    # 显示可选依赖状态
    if available_optional:
        print("✓ 可用的可选功能：")
        for module, description in available_optional:
            print(f"  - {module}: {description}")
    
    if missing_optional:
        print("- 可安装的可选功能：")
        for module, description in missing_optional:
            print(f"  - {module}: {description} (pip install {module})")
    
    # 检查主程序文件
    print("\n检查程序文件...")
    if not os.path.exists("invoiceApp.py"):
        print("✗ 找不到invoiceApp.py文件")
        input("按回车键退出...")
        sys.exit(1)
    print("✓ 程序文件存在")
    
    # 启动应用
    print("\n" + "=" * 60)
    print("启动发票管理工具...")
    print("=" * 60)
    
    try:
        # 导入并运行应用
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from invoiceApp import main as app_main
        app_main()
    except KeyboardInterrupt:
        print("\n用户取消操作")
    except Exception as e:
        print(f"\n应用运行出错：{e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
