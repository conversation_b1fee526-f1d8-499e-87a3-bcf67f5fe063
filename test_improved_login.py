#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的登录界面
"""

import sys
import os
import time
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_mock_server():
    """启动模拟服务器"""
    try:
        from mock_server import MockServer
        server = MockServer()
        if server.start():
            print("✓ 模拟服务器启动成功")
            return server
        else:
            print("✗ 模拟服务器启动失败")
            return None
    except Exception as e:
        print(f"✗ 启动模拟服务器时出错: {e}")
        return None

def test_login_scenarios():
    """测试不同的登录场景"""
    print("\n" + "=" * 60)
    print("登录场景测试")
    print("=" * 60)
    
    try:
        from login_window import LoginWindow
        
        # 创建临时登录窗口实例
        temp_window = LoginWindow()
        
        class TestArgs:
            def __init__(self, server, port, username, password):
                self.server = server
                self.port = port
                self.username = username
                self.password = password
                self.token = None
        
        # 测试场景
        scenarios = [
            {
                "name": "正确的登录信息",
                "args": TestArgs("localhost", "8881", "admin", "123456"),
                "expected": "成功"
            },
            {
                "name": "错误的密码",
                "args": TestArgs("localhost", "8881", "admin", "wrong_password"),
                "expected": "失败"
            },
            {
                "name": "错误的端口",
                "args": TestArgs("localhost", "9999", "admin", "123456"),
                "expected": "连接失败"
            },
            {
                "name": "不存在的服务器",
                "args": TestArgs("nonexistent.server.com", "8881", "admin", "123456"),
                "expected": "DNS失败"
            }
        ]
        
        for scenario in scenarios:
            print(f"\n测试: {scenario['name']}")
            print(f"服务器: {scenario['args'].server}:{scenario['args'].port}")
            print(f"用户: {scenario['args'].username}")
            
            start_time = time.time()
            try:
                token = temp_window._api_login_with_timeout(scenario['args'])
                end_time = time.time()
                duration = end_time - start_time
                print(f"✓ 登录成功 ({duration:.1f}s)")
                print(f"  Token: {token[:50]}..." if token else "  No token")
            except Exception as e:
                end_time = time.time()
                duration = end_time - start_time
                print(f"✗ 登录失败 ({duration:.1f}s): {e}")
            
            # 短暂延迟
            time.sleep(0.5)
        
        # 清理
        temp_window.root.destroy()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_gui_login():
    """测试GUI登录界面"""
    print("\n" + "=" * 60)
    print("GUI登录界面测试")
    print("=" * 60)
    
    try:
        from login_window import LoginWindow
        
        def on_login_success(args):
            print(f"\n🎉 GUI登录成功！")
            print(f"服务器: {args.server}:{args.port}")
            print(f"用户: {args.username}")
            print(f"Token: {args.token[:50]}..." if args.token else "No token")
        
        print("创建登录窗口...")
        login_window = LoginWindow(on_login_success=on_login_success)
        
        print("✓ 登录窗口已创建")
        print("\n测试指南:")
        print("1. 窗口大小: 500x580 (应该足够显示所有内容)")
        print("2. 默认值已填入 (localhost:8881, admin/123456)")
        print("3. 点击'登录'按钮测试正常登录")
        print("4. 修改端口为9999测试连接失败")
        print("5. 修改密码测试认证失败")
        print("6. 观察状态栏的反馈信息")
        print("7. 注意按钮在登录过程中的状态变化")
        
        print("\n改进功能:")
        print("✓ 10秒连接超时")
        print("✓ 详细的错误信息")
        print("✓ 自动恢复登录按钮")
        print("✓ 状态栏实时反馈")
        print("✓ 更大的按钮和更好的布局")
        
        print("\n窗口即将打开，请进行测试...")
        
        # 运行登录窗口
        login_window.run()
        
        print("GUI测试完成")
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("=" * 60)
    print("改进后的登录界面完整测试")
    print("=" * 60)
    
    # 启动模拟服务器
    print("启动模拟服务器...")
    server = start_mock_server()
    
    if server:
        try:
            # 等待服务器完全启动
            time.sleep(1)
            
            # 测试登录场景
            test_login_scenarios()
            
            # 测试GUI界面
            test_gui_login()
            
        finally:
            # 停止服务器
            print("\n停止模拟服务器...")
            server.stop()
            print("✓ 服务器已停止")
    else:
        print("⚠️  无法启动模拟服务器，跳过需要服务器的测试")
        print("您仍然可以测试GUI界面的布局和超时处理")
        
        # 仅测试GUI界面
        test_gui_login()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print("登录界面改进:")
    print("✓ 窗口大小调整为 500x580")
    print("✓ 按钮更大更显眼")
    print("✓ 添加了10秒连接超时")
    print("✓ 详细的错误处理和反馈")
    print("✓ 自动恢复UI状态")
    print("✓ 更好的布局和间距")
    print("✓ 实时状态显示")

if __name__ == "__main__":
    main()
