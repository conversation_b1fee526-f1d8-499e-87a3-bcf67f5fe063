#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试序号字段位置
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sequence_position():
    """测试序号字段位置"""
    print("=" * 60)
    print("测试序号字段位置")
    print("=" * 60)
    
    try:
        # 模拟登录参数
        class MockArgs:
            def __init__(self):
                self.server = "localhost"
                self.port = "8881"
                self.username = "admin"
                self.token = "test_token"
        
        from main_window import MainWindow
        
        print("创建主窗口...")
        args = MockArgs()
        main_window = MainWindow(args)
        
        print("✓ 主窗口已创建")
        
        # 检查列配置
        print("\n检查列配置:")
        columns = main_window.files_tree["columns"]
        print(f"   列定义: {columns}")
        
        # 检查列标题和顺序
        print("\n列标题和顺序:")
        column_info = [
            ("#0", "序号列"),
            ("name", "名称列"),
            ("type", "类型列"),
            ("path", "路径列")
        ]
        
        for col_id, col_desc in column_info:
            try:
                heading = main_window.files_tree.heading(col_id)["text"]
                config = main_window.files_tree.column(col_id)
                width = config.get("width", "未设置")
                anchor = config.get("anchor", "w")
                
                print(f"   {col_desc} ({col_id}):")
                print(f"     标题: {heading}")
                print(f"     宽度: {width}")
                print(f"     对齐: {anchor}")
                
                if col_id == "#0" and heading == "序号":
                    print(f"     ✓ 序号列正确设置为第一列")
                elif col_id == "name" and heading == "名称":
                    print(f"     ✓ 名称列正确设置为第二列")
                    
            except Exception as e:
                print(f"   获取{col_desc}配置失败: {e}")
        
        # 测试添加文件并检查数据结构
        print("\n测试数据结构:")
        test_files = [
            "test1.pdf",
            "test2.pdf", 
            "test3.pdf"
        ]
        
        for file_path in test_files:
            main_window._add_file_to_tree(file_path)
        
        # 检查数据结构
        children = main_window.files_tree.get_children()
        print(f"   添加了 {len(children)} 个文件")
        
        for item in children:
            # 获取#0列的内容（序号）
            seq_text = main_window.files_tree.item(item)["text"]
            # 获取其他列的内容
            values = main_window.files_tree.item(item)["values"]
            
            if len(values) >= 3:
                name, file_type, path = values[0], values[1], values[2]
                print(f"   序号: {seq_text} | 名称: {name} | 类型: {file_type} | 路径: {path}")
            else:
                print(f"   数据不完整: 序号={seq_text}, values={values}")
        
        # 清理
        main_window.root.destroy()
        
        print("\n✓ 序号位置测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_gui_sequence_position():
    """测试GUI中的序号位置"""
    print("\n" + "=" * 40)
    print("测试GUI序号位置")
    print("=" * 40)
    
    try:
        # 模拟登录参数
        class MockArgs:
            def __init__(self):
                self.server = "localhost"
                self.port = "8881"
                self.username = "admin"
                self.token = "test_token"
        
        from main_window import MainWindow
        
        print("创建主窗口...")
        args = MockArgs()
        main_window = MainWindow(args)
        
        print("✓ 主窗口已创建")
        
        # 添加一些测试数据
        print("\n添加测试数据...")
        test_files = [
            "invoice1.pdf",
            "invoice2.pdf",
            "report.pdf",
            "summary.pdf"
        ]
        
        for file_path in test_files:
            main_window._add_file_to_tree(file_path)
        
        # 更新列表显示状态
        main_window._update_list_display()
        
        print(f"   已添加 {len(test_files)} 个测试文件")
        
        print("\n测试指南:")
        print("1. 观察列表的列顺序")
        print("2. 第一列应该是'序号'，显示1, 2, 3, 4...")
        print("3. 第二列应该是'名称'，显示文件名")
        print("4. 第三列应该是'类型'，显示'文件'")
        print("5. 第四列应该是'路径'，显示完整路径")
        
        print("\n预期列顺序:")
        print("序号 | 名称 | 类型 | 路径")
        print("  1  | invoice1.pdf | 文件 | invoice1.pdf")
        print("  2  | invoice2.pdf | 文件 | invoice2.pdf")
        print("  3  | report.pdf   | 文件 | report.pdf")
        print("  4  | summary.pdf  | 文件 | summary.pdf")
        
        print("\n窗口将保持打开...")
        
        # 运行主窗口
        main_window.run()
        
        print("GUI测试完成")
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_sequence_operations():
    """测试序号操作"""
    print("\n" + "=" * 40)
    print("测试序号操作")
    print("=" * 40)
    
    try:
        # 模拟登录参数
        class MockArgs:
            def __init__(self):
                self.server = "localhost"
                self.port = "8881"
                self.username = "admin"
                self.token = "test_token"
        
        from main_window import MainWindow
        
        args = MockArgs()
        main_window = MainWindow(args)
        
        print("测试序号操作...")
        
        # 添加文件
        files_to_add = [
            "file1.pdf",
            "file2.pdf", 
            "file3.pdf"
        ]
        
        print("\n1. 添加文件:")
        for file_path in files_to_add:
            main_window._add_file_to_tree(file_path)
            print(f"   添加: {file_path}")
        
        # 显示当前状态
        print("\n   当前列表:")
        children = main_window.files_tree.get_children()
        for item in children:
            seq_text = main_window.files_tree.item(item)["text"]
            values = main_window.files_tree.item(item)["values"]
            if len(values) >= 3:
                name = values[0]
                print(f"     序号: {seq_text} | 名称: {name}")
        
        # 测试重复文件
        print("\n2. 添加重复文件:")
        duplicate_file = "file1.pdf"
        print(f"   再次添加: {duplicate_file}")
        main_window._add_file_to_tree(duplicate_file)
        
        print("\n   更新后列表:")
        children = main_window.files_tree.get_children()
        for item in children:
            seq_text = main_window.files_tree.item(item)["text"]
            values = main_window.files_tree.item(item)["values"]
            if len(values) >= 3:
                name = values[0]
                print(f"     序号: {seq_text} | 名称: {name}")
        
        # 测试移除
        print("\n3. 移除第一项:")
        if children:
            main_window.files_tree.selection_set(children[0])
            main_window._remove_selected()
            
            print("\n   移除后列表:")
            children = main_window.files_tree.get_children()
            for item in children:
                seq_text = main_window.files_tree.item(item)["text"]
                values = main_window.files_tree.item(item)["values"]
                if len(values) >= 3:
                    name = values[0]
                    print(f"     序号: {seq_text} | 名称: {name}")
        
        # 清理
        main_window.root.destroy()
        
        print("\n✓ 序号操作测试完成")
        
    except Exception as e:
        print(f"序号操作测试失败: {e}")

def main():
    """主函数"""
    print("序号字段位置测试")
    
    # 测试序号位置
    test_sequence_position()
    
    # 测试序号操作
    test_sequence_operations()
    
    # GUI测试
    test_gui_sequence_position()

if __name__ == "__main__":
    main()
