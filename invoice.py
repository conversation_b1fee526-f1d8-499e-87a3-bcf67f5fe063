# import pysnooper

import argparse
import os
from glob import glob
from shutil import copyfile

import apiUtil

# @pysnooper.snoop()
def has(s,v):
  for x in s:
    if (x==v):
      return True
  
  return False


# @pysnooper.snoop()
def invoice_rename(args):
  argsFormat = args.format
  sources=[]
  # print("args.invoice : {}".format(args.invoice))
  for source in args.invoice:
    # print("source : {}".format(source))
    if os.path.isdir(source):
      source="{}/*.*".format(source)
    files = glob(source)

    # print("Total Files Count : {}".format(len(files)))

    resultPath = "{}/".format(args.output)
    if not os.path.exists(resultPath):
      os.makedirs(resultPath)

    failPath = "{}fail/".format(resultPath)
    if not os.path.exists(failPath):
      os.makedirs(failPath)

    for f in files:
      if not has(sources,f):
        sources.append(f)
        if (os.path.exists(f)) and (not os.path.isdir(f)):
          print(f, end="")
          playload={'operations': '{"query":"mutation insertInvoiceInfo($uploadFile: Upload!) {insertInvoiceInfo(file: $uploadFile) {ok code message data {id fileMd5 invoicingDate invoiceNo amountIncludingTax tax buyerName buyerId sellerName sellerId}}}","variables":{"uploadFile":null}}',
    'map': '{"0": ["variables.uploadFile"]}'}
          files=[
            ('0',(os.path.basename(f),open(f,'rb'),'application/pdf'))
          ]
          result=apiUtil.graphqlUpload(args,playload,files).get('data').get('insertInvoiceInfo')
          # print(result)
          if (result.get('code')==200 and result.get('data')!=None and result.get('data').get('invoiceNo')!=None and result.get('data').get('invoicingDate')!=None and result.get('data').get('amountIncludingTax')!=None):
            newFilenamePart=argsFormat.format_map(result.get('data'))
            # print(newFilenamePart)
            newFilenameBase="{}{}".format(resultPath,newFilenamePart)
            index=0
            newFilename="{}.pdf".format(newFilenameBase)
            while (os.path.exists(newFilename)):
              index+=1
              newFilename="{}({}).pdf".format(newFilenameBase,index)
            print(" -> {}".format(newFilename))
            copyfile(f, newFilename)
          else:
            newFailFilenameBase="{}{}".format(failPath,os.path.basename(f))
            index=0
            newFailFilename="{}".format(newFailFilenameBase)
            while (os.path.exists(newFailFilename)):
              index+=1
              newFailFilename="{}({})".format(newFailFilenameBase,index)
            print(" -> FAIL : {}".format(newFailFilename))
            copyfile(f, newFailFilename)
        else:
          print("{} NOT EXISTS!".format(f))


# @pysnooper.snoop()
def main():
  global args

  parser = argparse.ArgumentParser(
    description='发票管理工具',
    usage="%(prog)s [option]",
    epilog='发票管理工具：可用于发票pdf文件整理（按发票内容命名pdf文件）。',
    formatter_class=argparse.ArgumentDefaultsHelpFormatter,
  )

  parser.version = '%(prog)s 0.10'

  parser.add_argument('-v', '--version', help="显示版本", action='version')
  # parser.add_argument('-s', '--server', help="rayoeServer地址（域名或ip）", default="*************")
  parser.add_argument('-s', '--server', help="rayoeServer地址（域名或ip）", default="localhost")
  parser.add_argument('-p', '--port', help="rayoeServer端口", default="8881")
  parser.add_argument('-u', '--username', help="用户名", default="admin")
  parser.add_argument('-w', '--password', help="密码", default="123456")
  parser.add_argument('-f', '--format', help="重命名文件格式定义。字段名以{}包裹，支持字段名包括：invoicingDate（开票日期），invoiceNo（发票号码），amountIncludingTax（含税金额），tax（税费），buyerName（购买方名称），buyerId（购买方税号），sellerName（销售方名称），sellerId（销售方税号）", default="{buyerId}_{invoicingDate}_{invoiceNo}_{amountIncludingTax}")
  parser.add_argument('-o', '--output', help="重命名文件保存目录", default="./output")
  parser.add_argument('-i', '--invoice', help="发票PDF文件或目录，支持通配符及多文件或目录", default=["*.pdf"], nargs="+")
  # parser.add_argument('-i', '--invoice', help="发票PDF文件或目录，支持通配符", default="*.pdf")

  args = parser.parse_args()
  # print(args.server, args.port, args.username, args.password)

  print("rayoe server({}:{}) -  {}\n".format(args.server, args.port, apiUtil.getVersion(args)))
  
  apiUtil.apiLogin(args)

  invoice_rename(args)

if __name__=='__main__':
  main()