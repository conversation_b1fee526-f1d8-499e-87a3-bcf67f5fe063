#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
发票管理工具 - 桌面应用版本
实现与命令行应用（invoice.py）相同的功能，并提供可视化操作界面

功能特性：
1. 登录界面：服务器地址、端口、用户名、密码、自动登录设置
2. 主界面：重命名格式定义、保存目录、PDF文件列表管理
3. 文件拖拽：支持拖拽文件和目录到应用中
4. Toast提示：操作成功/失败的轻提示反馈
5. 配置管理：自动保存和加载用户设置

作者：发票管理工具开发团队
版本：1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import threading

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from login_window import LoginWindow
from main_window import MainWindow
from config_manager import ConfigManager
import apiUtil

class InvoiceApp:
    """发票管理工具主应用程序"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.main_window = None
        self.login_args = None
        
    def run(self):
        """运行应用程序"""
        try:
            # 显示启动信息
            self._show_startup_info()
            
            # 创建并显示登录窗口
            login_window = LoginWindow(on_login_success=self._on_login_success)
            login_window.run()
            
        except Exception as e:
            messagebox.showerror("启动错误", f"应用程序启动失败：{e}")
            sys.exit(1)
    
    def _show_startup_info(self):
        """显示启动信息"""
        print("=" * 50)
        print("发票管理工具 - 桌面应用版本")
        print("版本：1.0.0")
        print("=" * 50)
        
        # 检查依赖
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查依赖库"""
        print("检查依赖库...")
        
        # 检查必需的库
        required_modules = ['requests', 'json']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✓ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"✗ {module} (缺失)")
        
        # 检查可选的库
        optional_modules = {
            'tkinterdnd2': '拖拽功能'
        }
        
        for module, description in optional_modules.items():
            try:
                __import__(module)
                print(f"✓ {module} ({description})")
            except ImportError:
                print(f"- {module} (可选，用于{description})")
        
        if missing_modules:
            error_msg = f"缺少必需的依赖库：{', '.join(missing_modules)}\n请使用 pip install 安装这些库"
            messagebox.showerror("依赖错误", error_msg)
            sys.exit(1)
        
        print("依赖检查完成")
        print("-" * 50)
    
    def _on_login_success(self, args):
        """登录成功回调"""
        self.login_args = args
        
        # 显示服务器信息
        try:
            version_info = apiUtil.getVersion(args)
            print(f"已连接到服务器：{args.server}:{args.port}")
            print(f"服务器版本：{version_info}")
            print("-" * 50)
        except Exception as e:
            print(f"获取服务器版本信息失败：{e}")
        
        # 创建并显示主窗口
        self._show_main_window()
    
    def _show_main_window(self):
        """显示主窗口"""
        try:
            self.main_window = MainWindow(self.login_args)
            self.main_window.run()
        except Exception as e:
            messagebox.showerror("界面错误", f"主界面启动失败：{e}")
            sys.exit(1)

def main():
    """主函数"""
    # 设置异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"未处理的异常：{exc_type.__name__}: {exc_value}"
        print(error_msg)
        
        # 如果tkinter可用，显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("程序错误", error_msg)
            root.destroy()
        except:
            pass
    
    sys.excepthook = handle_exception
    
    # 创建并运行应用程序
    app = InvoiceApp()
    app.run()

if __name__ == "__main__":
    main()
