#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI更新
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_update():
    """测试UI更新"""
    print("测试UI更新")
    print("=" * 30)
    
    try:
        from login_window import Lo<PERSON>Window
        
        def on_login_success(args):
            print(f"🎉 登录成功回调被调用！")
            print(f"服务器: {args.server}:{args.port}")
            print(f"用户: {args.username}")
            print(f"Token: {getattr(args, 'token', 'No token')}")
        
        # 创建登录窗口
        login_window = LoginWindow(on_login_success=on_login_success)
        
        # 模拟登录成功
        def simulate_login_success():
            print("模拟登录成功...")
            
            class MockArgs:
                def __init__(self):
                    self.server = "localhost"
                    self.port = "8881"
                    self.username = "admin"
                    self.token = "mock_token_123"
            
            args = MockArgs()
            
            # 直接调用处理函数
            login_window._handle_login_result("mock_token_123", args)
        
        # 3秒后模拟登录成功
        login_window.root.after(3000, simulate_login_success)
        
        print("登录窗口已创建")
        print("3秒后将模拟登录成功")
        print("观察UI是否正确更新")
        
        # 运行登录窗口
        login_window.run()
        
        print("窗口已关闭")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ui_update()
