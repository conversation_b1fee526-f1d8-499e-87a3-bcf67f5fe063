aiofiles==24.1.0
aiohappyeyeballs==2.4.6
aiohttp==3.10.11
aiohttp-cors==0.7.0
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
appnope==0.1.4
asttokens==3.0.0
attrs==25.1.0
# Editable install with no version control (autocut_api_test==0.1.0)
-e /Users/<USER>/work/XStudio/projects/autoCut/apiTest/autocut_api_test
bidict==0.23.1
blinker==1.9.0
Brotli==1.1.0
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
comm==0.2.2
ConfigArgParse==1.7
contourpy==1.3.2
cycler==0.12.1
debugpy==1.8.13
decorator==5.2.1
executing==2.2.0
-e git+https://github.com/exo-explore/exo.git@2702975762aa07529be5014b9d419f0b164ca70f#egg=exo
fastapi==0.115.12
filelock==3.17.0
Flask==3.1.0
flask-cors==5.0.1
Flask-Login==0.6.3
fonttools==4.57.0
frozenlist==1.5.0
fsspec==2025.2.0
gevent==24.11.1
geventhttpclient==2.3.3
greenlet==3.2.1
grpcio==1.67.0
grpcio-tools==1.67.0
h11==0.14.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.1
idna==3.10
iniconfig==2.1.0
ipykernel==6.29.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.4
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.8
locust==2.36.2
locust-cloud==1.20.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
matplotlib-inline==0.1.7
mcp==1.4.1
mdurl==0.1.2
msgpack==1.1.0
multidict==6.1.0
nest-asyncio==1.6.0
Nuitka==2.5.1
numpy==2.0.0
nvidia-ml-py==12.560.30
opencv-python==*********
ordered-set==4.1.0
packaging==24.2
pandas==2.2.3
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.6
pluggy==1.5.0
prometheus_client==0.20.0
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==5.28.1
psutil==6.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pydantic==2.9.2
pydantic-settings==2.8.1
pydantic_core==2.23.4
Pygments==2.19.1
pyparsing==3.2.3
pytest==8.3.5
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-engineio==4.12.0
python-multipart==0.0.20
python-socketio==5.13.0
pytz==2025.2
PyYAML==6.0.2
pyzmq==26.3.0
regex==2024.11.6
requests==2.32.3
rich==13.7.1
safetensors==0.5.3
scapy==2.6.1
setuptools==75.8.2
simple-websocket==1.1.0
six==1.17.0
sniffio==1.3.1
sse-starlette==2.2.1
stack-data==0.6.3
starlette==0.46.1
tinygrad @ git+https://github.com/tinygrad/tinygrad.git@ec120ce6b9ce8e4ff4b5692566a683ef240e8bc8
tk==0.1.0
tokenizers==0.20.3
tornado==6.4.2
tqdm==4.66.4
traitlets==5.14.3
transformers==4.46.3
typing_extensions==4.12.2
tzdata==2025.2
urllib3==2.3.0
uuid==1.30
uv==0.6.7
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.5
wcwidth==0.2.13
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wsproto==1.2.0
yarl==1.18.3
zope.event==5.0
zope.interface==7.2
zstandard==0.23.0
