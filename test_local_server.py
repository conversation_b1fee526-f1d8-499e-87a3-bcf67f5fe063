#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用本地测试服务器测试登录功能
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_local_server_connection():
    """测试本地服务器连接"""
    print("=" * 60)
    print("本地测试服务器连接测试")
    print("=" * 60)
    
    try:
        from login_window import LoginWindow
        
        # 创建临时登录窗口实例
        temp_window = LoginWindow()
        
        class TestArgs:
            def __init__(self, server, port, username, password):
                self.server = server
                self.port = port
                self.username = username
                self.password = password
                self.token = None
        
        # 测试本地服务器连接
        print("测试连接到本地服务器...")
        print("服务器: localhost:8881")
        print("用户: admin / 123456")
        
        args = TestArgs("localhost", "8881", "admin", "123456")
        
        start_time = time.time()
        try:
            token = temp_window._api_login_with_timeout(args)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✓ 登录成功 ({duration:.1f}s)")
            print(f"Token: {token[:50]}..." if token and len(token) > 50 else f"Token: {token}")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"✗ 登录失败 ({duration:.1f}s)")
            print(f"错误: {e}")
            
            # 提供故障排除建议
            print("\n故障排除建议:")
            if "连接超时" in str(e) or "连接" in str(e):
                print("1. 检查本地服务器是否正在运行")
                print("2. 确认服务器监听端口8881")
                print("3. 检查防火墙设置")
            elif "用户名或密码" in str(e):
                print("1. 确认用户名: admin")
                print("2. 确认密码: 123456")
                print("3. 检查服务器用户配置")
            else:
                print("1. 检查服务器日志")
                print("2. 确认API端点 /auth/login 可用")
                print("3. 检查服务器响应格式")
        
        # 清理
        temp_window.root.destroy()
        
    except Exception as e:
        print(f"测试初始化失败: {e}")
        import traceback
        traceback.print_exc()

def test_server_version():
    """测试服务器版本信息"""
    print("\n" + "=" * 40)
    print("服务器版本信息测试")
    print("=" * 40)
    
    try:
        import apiUtil
        
        class TestArgs:
            def __init__(self, server, port):
                self.server = server
                self.port = port
        
        args = TestArgs("localhost", "8881")
        
        print("获取服务器版本信息...")
        try:
            version = apiUtil.getVersion(args)
            print(f"✓ 服务器版本: {version}")
        except Exception as e:
            print(f"✗ 获取版本失败: {e}")
            
    except Exception as e:
        print(f"版本测试失败: {e}")

def test_gui_with_local_server():
    """使用本地服务器测试GUI登录"""
    print("\n" + "=" * 60)
    print("GUI登录界面测试 (本地服务器)")
    print("=" * 60)
    
    try:
        from login_window import LoginWindow
        
        def on_login_success(args):
            print(f"\n🎉 登录成功！")
            print(f"服务器: {args.server}:{args.port}")
            print(f"用户: {args.username}")
            if hasattr(args, 'token') and args.token:
                print(f"Token: {args.token[:50]}...")
            
            # 测试获取服务器版本
            try:
                import apiUtil
                version = apiUtil.getVersion(args)
                print(f"服务器版本: {version}")
            except Exception as e:
                print(f"获取服务器版本失败: {e}")
        
        print("创建登录窗口...")
        login_window = LoginWindow(on_login_success=on_login_success)
        
        print("✓ 登录窗口已创建")
        print("\n使用说明:")
        print("1. 默认设置已配置为本地服务器 (localhost:8881)")
        print("2. 默认用户名: admin")
        print("3. 默认密码: 123456")
        print("4. 点击'登录'按钮进行测试")
        print("5. 观察登录过程和结果反馈")
        
        print("\n功能特性:")
        print("✓ 10秒连接超时")
        print("✓ 详细错误信息")
        print("✓ 自动状态恢复")
        print("✓ 实时反馈显示")
        
        print("\n窗口即将打开...")
        
        # 运行登录窗口
        login_window.run()
        
        print("GUI测试完成")
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_server_status():
    """检查服务器状态"""
    print("检查本地服务器状态...")
    
    import socket
    
    try:
        # 尝试连接到服务器端口
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 8881))
        sock.close()
        
        if result == 0:
            print("✓ 服务器端口8881可访问")
            return True
        else:
            print("✗ 服务器端口8881不可访问")
            return False
            
    except Exception as e:
        print(f"✗ 端口检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("发票管理工具 - 本地服务器测试")
    print("使用本地测试服务器进行登录功能验证")
    
    # 检查服务器状态
    server_available = check_server_status()
    
    if server_available:
        print("✓ 本地服务器可访问，开始测试...")
        
        # 测试服务器版本
        test_server_version()
        
        # 测试登录连接
        test_local_server_connection()
        
        # 测试GUI界面
        test_gui_with_local_server()
        
    else:
        print("⚠️  本地服务器不可访问")
        print("\n请确保:")
        print("1. 本地测试服务器正在运行")
        print("2. 服务器监听端口8881")
        print("3. 防火墙允许本地连接")
        
        print("\n您仍然可以测试GUI界面布局和超时处理:")
        response = input("是否继续测试GUI界面? (y/n): ").lower().strip()
        
        if response == 'y':
            test_gui_with_local_server()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
